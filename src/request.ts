import { request, type RequestOptions } from '@umijs/max';
import { TokenManager, _performTokenRefreshAttempt } from './requestErrorConfig';

interface ApiResponse<T> {
  code: number;
  data: T;
  message: string;
}

// 使用统一的错误处理扩展原始请求
const extendedRequest = async <T = unknown>(
  url: string,
  options: RequestOptions = {},
): Promise<ApiResponse<T>> => {
  try {
    return await request<ApiResponse<T>>(url, options);
  } catch (error: any) {
    // 如果是token刷新成功的情况，重新发起请求
    if (error.name === 'TokenRefreshed') {
      // 重新发起请求，新的token已经在TokenManager中更新了
      return request<ApiResponse<T>>(url, options);
    }
    // 其他错误直接抛出，由统一错误处理器处理
    throw error;
  }
};

export { extendedRequest as request, TokenManager, _performTokenRefreshAttempt };