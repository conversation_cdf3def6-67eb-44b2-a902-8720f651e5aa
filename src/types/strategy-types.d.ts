// typings/strategy-types.d.ts

// --- 辅助类型定义 (可以放在命名空间外，如果它们是通用辅助类型；
// --- 或者放在命名空间内，如果它们只服务于 Strategy 命名空间)

// --- 主要的 Strategy 命名空间 ---
declare namespace Strategy {
  interface Real_ads_result {
    acos: number | null;
    clicks: number | null;
    cvr: number | null;
    impressions: number | null;
    orders: number | null;
    sales: number | null;
    spend: number | null;
  }
  interface Revision_history {
    [time: string]: {
      ver: number
      user_name: string,
      revision_content: string[]
    }
  }

  interface MonthlyTrendItem {
    month: string;
    demond: number | string;
    trend: string;
  }

  interface MarketTrends {
    monthly_trends: MonthlyTrendItem[];
  }

  interface WeekStrategyData {
    start_date: string;
    end_date: string;
    current_time: string;
    approach: string;
    rationale: string;
    revision_history: Revision_history;
    // TODO: ads_suggestion 接口应该没有返回，后期可以考虑删除
    ads_suggestion: {
      approach: string;
      primary_goal: {
        goal: string;
        rationale: string;
      };
      other_goals: string[];
      rationale: string;
    };
    primary_goal: {
      goal: string;
      rationale: string;
    };
    other_goals: string[];
    week_budget: {
      typical: number;
      min: number;
      max: number;
      last_week_budget: number;
      change_from_last_week: string;
      rationale: string;
    };
    bid_adjustment_range: {
      min: number;
      max: number;
      rational: string;
    };
    daily_strategy_suggestion: {
      [date: string]: {
        approach: string;
        guideline: string;
        budget_range: { min: number; max: number };
        bid_adjustment_range: { min: number; max: number };
      };
    };
    week_expected_result: {
      spend: {
        typical: number;
        min: number;
        max: number;
        last_week: number;
        change_from_last_week: string;
      };
      sales: {
        typical: number;
        min: number;
        max: number;
        last_week: number;
        change_from_last_week: string;
      };
      orders: {
        typical: number;
        min: number;
        max: number;
        last_week: number;
        change_from_last_week: string;
      };
      cvr: {
        typical: number;
        min: number;
        max: number;
        last_week: number;
        change_from_last_week: string;
      };
      acos: {
        typical: number;
        min: number;
        max: number;
        last_week: number;
        change_from_last_week: string;
      };
    };
    tips_for_week_after_target_week: string[];
  }

  interface DaypartAdsAdjustments {
    amount?: {
      new: number | null;
      old: number | null;
    };
    daypart_report: {
      job_id: string;
      current_time: string;
    };
    set_campaign_budget?: {
      set_campaign_budget: string | null;
    };
    set_placement_bidding?: {
      set_placement_bidding: string | null;
    };
  }

  interface Yesterday_campaign_budget {
    [campaign_id: string]: {
      budget: number,
      placement: {
        "Product Pages": number,
        "Rest of Search": number,
        "Top of Search": number
      }
    }
  }

  interface DayStrategyData {
    date: string;
    approach: string;
    rationale: string;
    day_budget: {
      amount: number;
      yesterday: number;
      change_from_yesterday: string;
      adjustment_range: {
        min: number;
        max: number;
      };
      rationale: string;
    };
    bid_adjustment_range: {
      typical: number;
      min: number;
      max: number;
      rationale: string;
    };
    revision_history: Revision_history;
    expected_results: {
      spend: number;
      orders: number;
      acos: number;
      sales: number;
      cvr: number;
    };
    campaign_budget: {
      campaign_id: number;
      amount: number;
      rationale: string;
      campaign_name: string;
      campaign_type: string;
      placements: {
        type: string,
        placement_type: string,
        old_bid: number,
        new_bid: number,
        bid_new: number
      }[]
    }[];
    hod_bid_adjustment: {
      adjustments: {
        adjustment: number,
        hour: number
      }[];
      rationale: string;
    };
    weekly_progress_analysis: {
      budget: {
        week_budget: string | number;
        week_budget_used: string | number;
        week_budget_left: string | number | null;
        week_budget_usage_rate: string | number;
      };
      sales: {
        target: string | number;
        current: string | number;
        progress: string | number;
      };
      cvr: {
        target: string | number;
        current: string | number;
        diff: string | number;
      };
      acos: {
        target: string | number;
        current: string | number;
        diff: string | number;
      };
      week_performance_vs_target: string;
      key_observations: string[];
    };
    ai_feedbackContent: string;
  }

  interface WeekAnalysisData {
    start_date: string;
    end_date: string;
    forecast: {
      market_preview: string[];
      metrics_forecast: {
        traffic: 'up' | 'down' | 'stable';
        spend: 'up' | 'down' | 'stable';
        sales: 'up' | 'down' | 'stable';
        acos: 'up' | 'down' | 'stable';
        cvr: 'up' | 'down' | 'stable';
      };
      overview: string;
    };
    revision_history: Revision_history;
    weekly_strategy: {
      week_start_date: string;
      strategy: string;
    }[];
    key_dates: {
      start_date: string;
      end_date: string;
      type: string[];
      name: string;
      confidence: 'low' | 'medium' | 'high';
      significance: 'low' | 'medium' | 'high';
      expected_impact: {
        traffic: 'low' | 'medium' | 'high';
        conversion: 'low' | 'medium' | 'high';
        competition: 'low' | 'medium' | 'high';
        rationale: string;
      };
      strategy: string;
    }[];
    swot: {
      strengths: string[];
      weaknesses: string[];
      opportunities: string[];
      threats: string[];
    };
    ads_suggestion: {
      approach: string;
      primary_goal: {
        goal: string;
        rationale: string;
      };
      other_goals: string[];
      rationale: string;
      weekly_strategy: {
        week_start_date: string;
        strategy: string;
      }[];
    };
    non_ads_suggestion: string[];
    google_searchs: {
      rendered_content: string;
      web_search_queries: string[];
      grounding_chunks: { title: string, url: string }[];
    };
  }

  interface DailyAdjustmentProposal {
    trigger_hour: number;
    overall_rationale: string;
    adjustments: {
      daily_strategy?: {
        approach_old: string;
        approach_new: string;
        rationale: string;
      };
      daily_budget?: {
        old: number;
        new: number;
        rationale: string;
      };
      hod: {
        hour: string;
        bid_new: string;
      }[];
      hod_rationale: string
      campaign?: API.CampaignAdjustmentItem[];
    };
    day_progress_analysis?: DayProgressAnalysis;
  };
}