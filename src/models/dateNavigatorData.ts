import { useQuery } from '@tanstack/react-query';
import { getReportDatesWithVer } from '@/services/ibidder_api/operation';

export type DateNavigatorMode = 'day' | 'week' | 'month';

// API 返回的日期数据类型
export interface DateItem {
  start_time: string;
  end_time: string;
  target_week: string;
  ver_list: {
    ver: number;
    is_select: number;
  }[];
}

type GetAllReportDatesResponse = {
  date: DateItem[];
  asin: string;
  profile_id: string;
  job_id: string;
};

export const normalizeJobToMode = (job_id?: string): DateNavigatorMode => {
  if (!job_id) return 'week';
  if (job_id === 'ads_strategy_day') return 'day';
  if (job_id === 'market_report_month') return 'month';
  // 包含 ads_strategy_week 或 market_report_week
  return 'week';
};

export const getDateNavigatorQueryKey = (
  mode: DateNavigatorMode,
  asin: string | null,
  profile_id: string | null,
) => ['dateNavigator', mode, asin || '', profile_id || ''];

interface FetchParams {
  asin: string;
  job_id: string;
  profile_id: string;
}

const fetchDateNavigatorDates = async (params: FetchParams) => {
  const res = await getReportDatesWithVer<GetAllReportDatesResponse>({
    asin: params.asin,
    job_id: params.job_id,
    profile_id: params.profile_id,
  });
  if (res?.code === 200 && res?.data?.date) {
    return res.data.date;
  }
  return [];
};

export function useDateNavigatorQuery(opts: {
  mode: DateNavigatorMode;
  asin: string | null;
  profile_id: string | null;
  job_id: string; // 按接口要求传入原始 job_id
  enabled?: boolean;
}) {
  const { mode, asin, profile_id, job_id, enabled = true } = opts;
  const key = getDateNavigatorQueryKey(mode, asin, profile_id);
  return useQuery({
    queryKey: key,
    queryFn: () => fetchDateNavigatorDates({ asin: asin as string, job_id, profile_id: profile_id as string }),
    // enabled 是boolean类型，如果为false，则不执行查询
    enabled: enabled && !!asin && !!profile_id && !!job_id,
    // staleTime 是number类型，如果为Infinity，则不缓存
    staleTime: 5 * 60 * 1000,
    // gcTime 是number类型，如果为Infinity，则不缓存
    gcTime: Infinity,
    // refetchOnWindowFocus 是boolean类型，如果为false，则不重新获取数据
    refetchOnWindowFocus: false,
    // refetchOnReconnect 是boolean类型，如果为false，则不重新连接
    refetchOnReconnect: false,
    // refetchOnMount 是boolean类型，如果为false，则不重新挂载
    refetchOnMount: false,
  });
}

