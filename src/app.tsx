import { AvatarDropdown, AvatarName } from '@/components';
import { LinkOutlined } from '@ant-design/icons';
import type { Settings as LayoutSettings } from '@ant-design/pro-components';
// import { SettingDrawer } from '@ant-design/pro-components';
import type { RunTimeLayoutConfig } from '@umijs/max';
import { history, Link } from '@umijs/max';
import defaultSettings from '../config/defaultSettings';
import { loginPath, registerPath, homePath } from '../config/routes';
import { errorConfig, TokenManager } from './requestErrorConfig';
import { getUserPermissions, guestLogin } from '@/services/ibidder_api/user';
import NoFoundPage from '@/pages/403';

import React from 'react';
import { AuthUtils } from '@/utils/auth';
import { Button, message, ConfigProvider } from 'antd';
import { sourceImageUrl } from '@/utils/common';
import DemoTip from '@/components/DemoTip';
const isDev = process.env.NODE_ENV === 'development';
import zhCN from 'antd/es/locale/zh_CN';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

const queryClient = new QueryClient();

/**
 * 检查当前路径是否需要登录验证
 * @param pathname 当前路径
 * @returns 是否需要登录验证
 */
const isAuthRequiredPath = (pathname: string): boolean => {
  return pathname !== loginPath && pathname !== homePath && pathname !== registerPath;
};

// 全局变量，用于跟踪定时器是否已经设置
let globalTokenCheckInterval: NodeJS.Timeout | null = null;

/**
 * 从URL中解析token参数并保存到localStorage
 */
const processTokenFromUrl = () => {
  const urlParams = new URLSearchParams(window.location.search);
  const accessToken = urlParams.get('access_token');
  const accessExpires = urlParams.get('access_expires');
  const refreshToken = urlParams.get('refresh_token');
  const refreshExpires = urlParams.get('refresh_expires');

  if (accessToken && refreshToken) {
    // 保存token到localStorage
    TokenManager.setAccessToken(accessToken);
    TokenManager.setRefreshToken(refreshToken);
    if (accessExpires) {
      TokenManager.setAccessTokenExpires(parseInt(accessExpires));
    }
    // 设置refresh_token过期时间
    if (refreshExpires) {
      TokenManager.setRefreshTokenExpires(parseInt(refreshExpires));
    }

    // 解析JWT获取用户信息
    try {
      const userInfo = AuthUtils.parseJwt(accessToken);
      if (userInfo && userInfo.user_id) {
        // 保存用户信息
        AuthUtils.saveUserInfo({
          id: userInfo.user_id,
          username: userInfo.username,
          // 其他用户信息字段可能需要根据实际JWT内容调整
          avatar: sourceImageUrl('default_avatar.png'),
        });

        // 从URL中移除token参数并替换当前历史记录
        const cleanUrl = window.location.pathname +
          (window.location.search ? '?' + Array.from(urlParams.entries())
            .filter(([key]) => !['access_token', 'access_expires', 'refresh_token', 'refresh_expires'].includes(key))
            .map(([key, value]) => `${key}=${value}`)
            .join('&') : '');

        window.history.replaceState({}, document.title, cleanUrl || window.location.pathname);
        return true;
      }
    } catch (error) {
      console.error('解析JWT token失败:', error);
    }
  }
  return false;
};

/**
 * 清除token检查定时器
 */
function clearTokenCheckInterval() {
  if (globalTokenCheckInterval) {
    console.log('清除定时器');
    clearInterval(globalTokenCheckInterval);
    globalTokenCheckInterval = null;
  }
}

// 在页面卸载时清除定时器
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', clearTokenCheckInterval);
}

/**
 * @see  https://umijs.org/zh-CN/plugins/plugin-initial-state
 * */
export async function getInitialState(): Promise<{
  settings?: Partial<LayoutSettings>;
  currentUser?: API.CurrentUser;
  loading?: boolean;
  permissions?: API.Permissions;
  fetchUserInfo?: () => Promise<API.CurrentUser | undefined>;
}> {
  // 检查URL中是否包含token并处理
  const tokenProcessed = processTokenFromUrl();

  // 如果是无需登录模式，直接返回模拟用户数据和设置
  if (process.env.REACT_APP_NO_AUTH) {
    return {
      currentUser: {
        name: 'Guest User',
        avatar: sourceImageUrl('default_avatar.png'),
        userid: '00000001',
        email: '<EMAIL>',
        access: 'admin',
      },
      settings: defaultSettings as Partial<LayoutSettings>,
    };
  }

  const fetchUserInfo = async () => {
    // 检查JWT token或传统cookie token
    if (AuthUtils.isLoggedIn()) {
      // 从localStorage获取用户信息
      const storedUserInfo = AuthUtils.getUserInfo();

      if (storedUserInfo) {
        return {
          name: AuthUtils.getUserDisplayName(),
          avatar: storedUserInfo.avatar || sourceImageUrl('default_avatar.png'),
          userid: storedUserInfo.id.toString(),
          email: storedUserInfo.email || '',
          phone: storedUserInfo.phone || '',
          user_type: storedUserInfo.user_type || 0,
          access: 'admin', // 根据user_type可以进一步判断权限
        };
      } else {
        // 如果刚刚处理了URL中的token，不跳转到登录页面，而是重新获取用户信息
        if (tokenProcessed) {
          // 重新调用自身以获取用户信息
          return fetchUserInfo();
        }
        AuthUtils.logout();
        return undefined;
      }
    } else {
      // 如果刚刚处理了URL中的token，不跳转到登录页面，而是重新获取用户信息
      if (tokenProcessed) {
        // 重新调用自身以获取用户信息
        return fetchUserInfo();
      }
      AuthUtils.logout();
    }
  };

  // 如果不是登录页面和注册页面，执行
  const { location } = history;
  if (location.pathname !== '/' && isAuthRequiredPath(location.pathname)) {
    const currentUser = await fetchUserInfo();
    try {
      const permissionsRes = await getUserPermissions();
      if (permissionsRes.code === 200) {
        return {
          fetchUserInfo,
          currentUser,
          permissions: permissionsRes.data as unknown as API.Permissions,
          settings: defaultSettings as Partial<LayoutSettings>,
        };
      }
    } catch (error) {
      console.log('获取用户权限失败', error);
    }
    return {
      fetchUserInfo,
      currentUser,
      settings: defaultSettings as Partial<LayoutSettings>,
    };
  }
  return {
    fetchUserInfo,
    settings: defaultSettings as Partial<LayoutSettings>,
  };
}

// ProLayout 支持的api https://procomponents.ant.design/components/layout
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const layout: RunTimeLayoutConfig = ({ initialState, setInitialState }) => {
  // 启动定时检查
  const startTokenCheck = () => {
    // 避免重复设置定时器，使用全局变量检查
    if (globalTokenCheckInterval) {
      return;
    }

    // 定时检查token是否即将过期（每3秒检查一次）
    globalTokenCheckInterval = setInterval(async () => {
      if (AuthUtils.isLoggedIn()) {
        await AuthUtils.autoRefreshAccessTokenIfNeeded(10);
      }
    }, 9 * 1000);

    // 立即执行一次检查
    if (AuthUtils.isLoggedIn()) {
      AuthUtils.autoRefreshAccessTokenIfNeeded(10);
    }
  };

  // 初始化时启动定时检查
  startTokenCheck();

  // 设置组件卸载时的清理函数
  if (typeof window !== 'undefined') {
    window.addEventListener('unload', () => {
      clearTokenCheckInterval();
    });
  }

  return {
    // actionsRender: () => [<Question key="doc" />, <SelectLang key="SelectLang" />],
    actionsRender: () => {
      const handleGuestLogin = async () => {
        try {
          const res = await guestLogin() as any;
          if (res.code === 200) {
            message.success('登录成功！');
            AuthUtils.handleLoginSuccess(res.data);
            localStorage.setItem('isGuestMode', 'true');
            sessionStorage.removeItem('guestTipClosed');
            // re-fetch user info
            const storedUserInfo = AuthUtils.getUserInfo();
            if (storedUserInfo) {
              const userInfo = {
                name: AuthUtils.getUserDisplayName(),
                avatar: storedUserInfo.avatar || sourceImageUrl('default_avatar.png'),
                userid: storedUserInfo.id.toString(),
                email: storedUserInfo.email || '',
                phone: storedUserInfo.phone || '',
                user_type: storedUserInfo.user_type || 0,
                access: 'admin',
              };
              setInitialState((s) => ({
                ...s,
                currentUser: userInfo,
              }));
            }
            window.location.href = '/admin/listing';
          } else {
            message.error(res.message);
          }
        } catch (error) {
          console.error('登录失败，请重试！', error);
        }
      };
      if (localStorage.getItem('isGuestMode') !== 'true' && initialState?.permissions?.show_visitor) {
        return [
          <Button shape="round" onClick={handleGuestLogin}>
            游客体验
          </Button>,
        ];
      }
      return [];
    },
    avatarProps: {
      src: initialState?.currentUser?.avatar,
      title: <AvatarName />,
      render: (_, avatarChildren) => {
        return (
          <>版本号 :
            <a
              href="https://nsfhu34274.feishu.cn/docx/HojQdIcaooOZlrxnU7HccP3Wnsb?from=from_copylink"
              target="_blank"
              rel="noopener noreferrer"
              style={{marginRight: '1em'}}
            >1.1.4</a>
            <AvatarDropdown menu>{avatarChildren}</AvatarDropdown>
          </>
        );
      },
    },
    // waterMarkProps: {
    //   content: initialState?.currentUser?.name,
    // },
    // footerRender: () => <Footer />,
    onPageChange: () => {
      // 在无需登录模式下，不进行登录检查和重定向
      if (process.env.REACT_APP_NO_AUTH) {
        return;
      }

      const { location } = history;
      // 只在需要登录模式下进行登录检查
      if (!initialState?.currentUser && isAuthRequiredPath(location.pathname)) {
        AuthUtils.logout();
        return;
      }
    },
    bgLayoutImgList: [
      {
        src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/D2LWSqNny4sAAAAAAAAAAAAAFl94AQBr',
        left: 85,
        bottom: 100,
        height: '303px',
      },
      {
        src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/C2TWRpJpiC0AAAAAAAAAAAAAFl94AQBr',
        bottom: -68,
        right: -45,
        height: '303px',
      },
      {
        src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/F6vSTbj8KpYAAAAAAAAAAAAAFl94AQBr',
        bottom: 0,
        left: 0,
        width: '331px',
      },
    ],
    links: isDev
      ? [
        <Link key="openapi" to="/umi/plugin/openapi" target="_blank">
          <LinkOutlined />
          <span>OpenAPI 文档</span>
        </Link>,
      ]
      : [],
    menuHeaderRender: undefined,
    // 自定义 403 页面
    unAccessible: <NoFoundPage />,
    // 增加一个 loading 的状态
    childrenRender: (children) => {
      // if (initialState?.loading) return <PageLoading />;
      return (
        <QueryClientProvider client={queryClient}>
          <ConfigProvider locale={zhCN}>
            <DemoTip />
            {children}
            {/* {isDev && (
              <SettingDrawer
                disableUrlParams
                enableDarkTheme
                settings={initialState?.settings}
                onSettingChange={(settings) => {
                  setInitialState((preInitialState) => ({
                    ...preInitialState,
                    settings,
                  }));
                }}
              />
            )} */}
          </ConfigProvider>
        </QueryClientProvider>
      );
    },
    ...initialState?.settings,
  };
};

/**
 * @name request 配置，可以配置错误处理
 * 它基于 axios 和 ahooks 的 useRequest 提供了一套统一的网络请求和错误处理方案。
 * @doc https://umijs.org/docs/max/request#配置
 */
export const request = {
  ...errorConfig,
  requestInterceptors: [
    (url: string, options: any) => {
      // 在无需登录模式下，跳过权限检查
      if (process.env.REACT_APP_NO_AUTH === 'true') {
        return {
          url,
          options: { ...options, skipErrorHandler: true },
        };
      }

      return { url, options };
    },
  ],
};
