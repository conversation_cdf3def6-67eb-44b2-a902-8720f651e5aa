declare namespace API {
  type ErrorResponse = {
    /** Detail */
    detail: string;
  };

  type GetListingInfoParams = {
    /** The Amazon Standard Identification Number (ASIN) of the listing */
    parent_asin: string;
    profile_id: string;
  };

  type GetSearchTermsParams = {
    asin: string;
    /** Start date in YYYY-MM-DD format. Defaults to 90 days before end_date */
    start_date?: string | null;
    /** End date in YYYY-MM-DD format. Defaults to yesterday */
    end_date?: string | null;
  };

  type HTTPValidationError = {
    /** Detail */
    detail?: ValidationError[];
  };

  type FilterOptions = {
    /** 国家列表 */
    countries: string[];
    /** 店铺名称列表 */
    store_names: string[];
    /** 负责人列表 */
    owners: string[];
    /** 店铺列表 */
    seller_list: {
      profile_id: string;
      country: string;
      store_name: string;
    }[];
    /** 货币列表 */
    currencies: string[];
    /** 品牌列表 */
    brands: string[];
    /** 状态列表 */
    statuses: string[];
  };

  type OperationResult = {
    /** 成功处理的数量 */
    success_count: number;
    /** 失败处理的数量 */
    failed_count: number;
    /** 失败的项目列表 */
    failed_items: any[];
    /** 可选的消息 */
    message?: string;
  };

  type PromotionPlan = {
    discount_price: number;
    promotion_type: string;
    time_range: string[];
    has_promotion: boolean;
    [property: string]: any;
  };

  type ListingInfo = {
    ad_target: string;
    asin: string;
    asins: string[];
    category_id: string;
    category_name: string;
    competitor: string[];
    country: string;
    currency: string;
    createTime: string;
    image_url: string;
    inventory_status: string;
    online_days: number;
    owners: string[];
    parent_asin: string;
    price: number;
    profile_id: number;
    promotion_plan: PromotionPlan;
    rating: number;
    review_count: number;
    root_category_id: string;
    root_category_name: string;
    sales: number;
    store_name: string;
    title: string;
    updateTime: string;
    url: string;
    ads_rules?: any;
    ai_instruction?: {
      content: string;
      id: number;
      name: string;
    };
    [property: string]: any;
  };

  type ListingList = {
    /** Listings */
    listings: ListingInfo[];
  };

  type ValidationError = {
    /** Location */
    loc: (string | number)[];
    /** Message */
    msg: string;
    /** Error Type */
    type: string;
  };

  type GetOperationHistoryParams = {
    /** Asin */
    asin: string;
    /** Marketplace */
    marketplace?: string;
    /** Start Time */
    startTime?: string;
    /** End Time */
    endTime?: string;
    /** Page Number */
    pageNo?: number;
    /** Page Size */
    pageSize?: number;
    /** Sort Field */
    sortField?: string;
    /** Sort */
    sort?: string;
  };

  type GetAIWorkHistoryParams = {
    /** Asin */
    asin: string;
    /** Profile Id */
    profile_id: string;
    /** Role */
    role: string;
    /** Start Time */
    start_time?: string;
    /** End Time */
    end_time?: string;
    /** Page Number */
    page_no?: number;
    /** Page Size */
    page_size?: number;
  };

  type GetAdStrategyParams = {
    date: string;
    asin: string;
    profile_id?: string | number;
    job_id?: string;
  };

  type UpdateWeekStrategyParams = {
    es_id: string;
    data: {
      ads_strategy_week?: {
        [property: string]: any;
      }
    };
    [property: string]: any;
  };

  type GetBaseReportParams = {
    asin: string;
    current_time: string;
    job_id: string;
    profile_id: string;
    target_job_id?: string;
  };

  type AccountLoginParams = {
    dynamic_code?: string;
    email?: string;
    password?: string;
    phone?: string;
    /**
     * 用户名
     */
    username?: string;
    [property: string]: any;
  };
  type GetDynamicCodeParams = {
    email?: string;
    phone?: string;
    [property: string]: any;
  };

  type RegisterParams = {
    dynamic_code: string;
    email: string;
    password: string;
    phone: string;
    [property: string]: any;
  };

  type RefreshTokenParams = {
    refresh_token: string;
  };

  type ListingData = {
    code: number;
    message: string;
    data: {
      parent_asin: string;
      profile_id: string;
      title: string;
      image_url: string;
      url: string;
      price: number;
      asins: string[];
      rating: number;
      review_count: number;
      store_name: string;
      country: string;
      category_id: string;
      category_name: string;
      online_days: number;
      root_category_id: string;
      root_category_name: string;
      total_sales: number;
      variants: {
        asin: string;
        title: string;
        price: number;
        image_url: string;
        url: string;
        sales: number;
      }[];
    }[];
  };

  type SavelistingResult = {
    code: number;
    message: string;
    data: any;
  };

  type LoginResult = {
    status?: string;
    type?: string;
    currentAuthority?: string;
  };

  // 根据新的API响应结构更新类型定义
  type MessageItem = {
    id: number;
    user_id: number;
    sender_id: number;
    message_type: number;
    title: string;
    content: string | null;
    is_read: number;
    is_deleted: number;
    extra_data: Record<string, any>; // extra_data 结构复杂，暂时用 Record<string, any>
    created_at: string;
    updated_at: string;
    reviewer_id?: number;
    reviewer_name?: string;
  };

  type MessageListResponse = {
    list: MessageItem[];
    total: number;
    pages: number;
    current: number;
    size: number;
  };

  type EditReportParams = {
    asins: string[];
    current_time: string;
    es_id: string;
    job_id: string;
    parent_asin: string;
    profile_id: string;
    role: string;
    country: string;
    data: any;
  };

  type GetAdStrategyResponseData<T> = {
    asin: string;
    asins: string[];
    current_time: string;
    duration_seconds: number;
    end_time: string;
    es_id: string;
    job_id: string;
    job_name: string;
    profile_id: string;
    can_edit: boolean;
    result: T;
    role: string;
    start_time: string;
    success: boolean;
    start_date: string;
    end_date: string;
    target_week: null;
  };

  type GetAdStrategyResponse = {
    code: number;
    message: string;
    data: GetAdStrategyResponseData<any>;
  };

  type TableList<T> = {
    list: T[];
    current: number;
    pages: number;
    size: number;
    total: number;
  };

  type ReportUpdateStatusResponse = {
    es_id: string
    is_create_success: boolean
    job_id: string
    role: string
  };

  type CampaignAdjustmentItem = {
    campaign_type: string;
    campaign_id: number;
    campaign_name: string;
    budget_old: number | string;
    budget_new: number | string;
    rationale: string;
    bid_old: number | string; // 假设是以小数形式表示的百分比, e.g., 0.03 for 3%
    bid_new: number | string; // 假设是以小数形式表示的百分比
    current_acos: string;
    current_cvr: string;
    current_sales: string;
    current_spend: string;
    placements: {
      type: string,
      old_bid: number,
      new_bid: number
    }[]
  };

  type DayProgressAnalysis = {
    budget: {
      day_budget: string;
      day_budget_spend: string;
      day_budget_left: string | null;
      current_spend_progress: string;
      target_spend_progress: string;
    };
    sales: {
      sales_target: string;
      sales_current: string;
      sales_left: string | null;
      current_progress: string;
      target_progress: string;
    };
    cvr: {
      target: string;
      current: string;
      diff: string;
    };
    acos: {
      target: string;
      current: string;
      diff: string;
    };
    key_observations: string[];
    day_performance_vs_target: string;
  }

  type TAds_data_hod = {
    hod: string;
    spend: string;
    sales: string;
    cvr: string;
    acos: string;
    spend_totle: string;
    sales_totle: string;
    cvr_totle: string;
    acos_totle: string;
    orders: string;
    orders_totle: string;
    clicks: string;
    clicks_totle: string;
  }

  type AdsTrendResponse = {
    list: {
      startDate: string;
      budget: number;
      spend: number;
      budgetUsage: string;
      impressions: number;
      clicks: number;
      orders: number;
      units: number;
      sales: number;
      valid_days: number;
      cpc: number;
      ctr: string;
      cvr: string;
      acos: string;
      cpa: number;
      avgPrice: number;
      sku_orders?: number;
      sku_sales?: number;
      spend_ratio?: string;
      sales_ratio?: string;
      strategy_expected_data: {
        expected_results?: {
          acos: string;
          cvr: string;
          sales: string;
          spend: string;
        };
      };
    }[];
    sum: {
      startDate: string;
      budget: number;
      spend: number;
      impressions: number;
      clicks: number;
      orders: number;
      units: number;
      sales: number;
      budgetUsage: string;
      cpc: number;
      ctr: string;
      cvr: string;
      acos: string;
      cpa: number;
      avgPrice: number;
      sku_orders?: number;
      sku_sales?: number;
      spend_ratio?: string;
      sales_ratio?: string;
      start_time: string;
      end_time: string;
    },
    previous_sum: {
      startDate: string;
      budget: number;
      spend: number;
      impressions: number;
      clicks: number;
      orders: number;
      units: number;
      sales: number;
      budgetUsage: string;
      cpc: number;
      ctr: string;
      cvr: string;
      acos: string;
      cpa: number;
      avgPrice: number;
      sku_orders?: number;
      sku_sales?: number;
      spend_ratio: string;
      sales_ratio: string;
      acos_ratio: string;
      cvr_ratio: string;
      orders_ratio?: string;
      cpa_ratio?: string;
      start_time: string;
      end_time: string;
    };
  }

  type Permissions = {
    shop_auth_perm: 'MANAGE' | 'NO_PERMISSION';
    ai_switch_perm: 'MANAGE' | 'NO_PERMISSION';
    account_management_perm: 'MANAGE' | 'NO_PERMISSION';
    group_management_perm: 'MANAGE' | 'NO_PERMISSION';
    role_management_perm: 'MANAGE' | 'NO_PERMISSION';
    operation_log_perm: 'MANAGE' | 'VIEW' | 'NO_PERMISSION';
    has_shop: boolean;
    show_visitor: boolean;
  };

  type NoticeIconList = {
    data?: NoticeIconItem[];
  };
  type ListingDataResponse = {
    [key: string]: {
      current_list: {
        "date": string,
        "weekday": string,
        "budget": number,
        "impressions": number,
        "clicks": number,
        "orders": number,
        "units": number,
        "sales": number,
        "spend": number,
        "campaignType": string,
        "campaignId": number,
        "topOfSearchImpressionShare": number,
        "sku_orders": number,
        "sku_sales": number,
        "acos": string,
        "cvr": string,
        "ctr": string,
        "cpc": string,
        "cpa": string
      }[];
      previous_total: {
        "budget": number,
        "spend": number,
        "impressions": number,
        "clicks": number,
        "orders": number,
        "units": number,
        "sales": number,
        "sku_orders": number,
        "sku_sales": number,
        "topOfSearchImpressionShare": number,
        "acos": string,
        "cvr": string,
        "ctr": string,
        "cpc": string,
        "cpa": string
      }
    },
  }
}
