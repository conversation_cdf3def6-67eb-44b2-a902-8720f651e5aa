import React, { useState, useEffect, useLayoutEffect, useRef, useCallback, useMemo } from 'react';
import { Button, Space, message, Select, Tooltip, Checkbox } from 'antd';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import isBetween from 'dayjs/plugin/isBetween';
import { useSearchParams } from '@umijs/max';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(isBetween);
import styles from './index.less';
import { setDefaultReport } from '@/services/ibidder_api/operation';
import { getDayOfWeek } from '@/utils/bus';

import { useQueryClient } from '@tanstack/react-query';
import { useDateNavigatorQuery, normalizeJobToMode, DateItem } from '@/models/dateNavigatorData';

interface DateNavigatorProps {
  initialDate?: string;
  current_time: string;
  job_id?: string;
  dateType: 'single' | 'range'; // 新增：区分单日期和区间日期
  es_id: string;
  // 新增：API调用回调函数
  onDateChange?: (apiDate: string, isFirst?: boolean) => Promise<void>;
  onVersionChange?: (esId: string) => Promise<void>;
}

// 版本相关状态
interface VersionState {
  current: number;
  default: number;
}

/* 日期导航器组件 根据传入的 country 获取时区对应的日期 */
const DateNavigator: React.FC<DateNavigatorProps> = (props) => {
  const {
    initialDate,
    job_id,
    dateType = 'single',
    es_id,
    onDateChange,
    onVersionChange
  } = props
  const [searchParams] = useSearchParams();
  const asin = searchParams.get('asin') as string;
  const profile_id = searchParams.get('profile_id') as string;
  const scrollWrapperRef = useRef<HTMLDivElement>(null);
  const didInitRef = useRef(false);


  // 状态管理优化：合并相关状态
  const [selectedDate, setSelectedDate] = useState('');
  const [showNavButtons, setShowNavButtons] = useState(false);
  const [versionState, setVersionState] = useState<VersionState>({
    current: 0,
    default: 0,
  });

  // 更新版本状态的工具函数
  const updateVersionState = useCallback((dateItem: DateItem) => {
    const currentVersion = dateItem.ver_list[0]?.ver || 0;
    const defaultVersion = dateItem.ver_list.find(v => v.is_select === 1)?.ver || 0;
    setVersionState({
      current: currentVersion,
      default: defaultVersion,
    });
  }, []);

  // 使用 react-query 获取日期数据（全局缓存：按 day/week/month）
  const mode = normalizeJobToMode(job_id || '');
  const queryClient = useQueryClient();
  const { data: dateItems = [] } = useDateNavigatorQuery({
    mode,
    asin,
    profile_id,
    job_id: (job_id || '') + '',
    enabled: !!asin && !!profile_id && !!job_id,
  });

  // 根据显示字符串找到日期项（接口约定 start_time 与 target_week 相同，这里仅使用 start_time 匹配）
  const findSelectedDateItem = useCallback((value: string): DateItem | null => {
    if (!dateItems || dateItems.length === 0) return null;
    const found = dateItems.find(item => item.start_time === value) || null;
    if (!found) {
      message.error('无法找到对应的日期数据');
    }
    return found;
  }, [dateItems]);
  const selectedDateItem = useMemo(() => {
    if (!dateItems || dateItems.length === 0 || !selectedDate) return null;
    return dateItems.find(i => i.start_time === selectedDate) || null;
  }, [dateItems, selectedDate]);


  useEffect(() => {
    if (!dateItems || dateItems.length === 0) return;
    if ((selectedDate && selectedDateItem) || didInitRef.current) return;
    const targetItem = (initialDate && dateItems.find(item => item.start_time === initialDate)) || dateItems[0];
    setSelectedDate(targetItem.start_time);
    didInitRef.current = true;
  }, [dateItems, initialDate, selectedDate, selectedDateItem]);

  // 当派生的选中项发生变化时，更新版本状态
  useEffect(() => {
    if (selectedDateItem) {
      updateVersionState(selectedDateItem);
    }
  }, [selectedDateItem, updateVersionState]);


  useLayoutEffect(() => {
    const checkOverflow = () => {
      if (scrollWrapperRef.current) {
        const { scrollWidth, clientWidth } = scrollWrapperRef.current;
        setShowNavButtons(scrollWidth > clientWidth);
      }
    };

    checkOverflow();
    window.addEventListener('resize', checkOverflow);
    return () => window.removeEventListener('resize', checkOverflow);
  }, [dateItems]);

  // 滚动处理函数优化
  const createScrollHandler = useCallback((direction: 'prev' | 'next') => () => {
    if (scrollWrapperRef.current) {
      const scrollAmount = scrollWrapperRef.current.offsetWidth * 0.5;
      const scrollDirection = direction === 'prev' ? -scrollAmount : scrollAmount;
      scrollWrapperRef.current.scrollBy({
        left: scrollDirection,
        behavior: 'smooth'
      });
    }
  }, []);

  const handlePrev = createScrollHandler('prev');
  const handleNext = createScrollHandler('next');

  const handleDateClick = useCallback(async (selectedDate: string) => {
    setSelectedDate(selectedDate);

    if (!asin || !profile_id) {
      console.error('缺少必要参数 asin 或 profile_id');
      return;
    }

    // 根据显示的日期字符串找到对应的原始数据项
    const item = findSelectedDateItem(selectedDate);
    if (!item) return;

    // 判断是否是第一个日期（最新的日期）
    const isFirstDate = dateItems.length > 0 && dateItems[0].start_time === item.start_time;

    // 使用 start_time 作为 API 调用的日期参数
    if (onDateChange) {
      await onDateChange(item.start_time, isFirstDate);
    }
  }, [asin, profile_id, findSelectedDateItem, onDateChange, dateItems]);

  // 版本切换处理函数
  const handleVersionChange = useCallback(async (version: number) => {
    setVersionState(prev => ({ ...prev, current: version }));
    if (!selectedDateItem) return;

    const esIdPrefix = es_id.split('_')[0];
    if (esIdPrefix && onVersionChange) {
      const targetEsId = `${esIdPrefix}_${version}`;
      await onVersionChange(targetEsId);
    }
  }, [selectedDateItem, es_id, onVersionChange]);

  // 设置默认版本
  const handleSetDefaultVersion = useCallback(async (version: number) => {
    try {
      await setDefaultReport({
        asin,
        current_time: props.current_time,
        job_id: (job_id || '') + '',
        profile_id,
        ver: version,
      });
      // 仅刷新当前模式(day/week/month)下的全局缓存
      await queryClient.invalidateQueries({ queryKey: ['dateNavigator', mode, asin || '', profile_id || ''] });
    } catch (error) {
      console.error('设置默认版本失败, 请联系管理员。');
    }
  }, [asin, props.current_time, job_id, profile_id, queryClient, mode]);

  // 格式化日期显示
  const formatDateDisplay = useCallback((item: DateItem) => {
    if (job_id === 'market_report_month') {
      return dayjs(item.start_time).format('YYYY-MM月');
    }
    return dateType === 'single' ?
      <div style={{ lineHeight: '1.3em', padding: '2px 0' }}>
        {item.start_time}
        <br />
        {getDayOfWeek(item.start_time)}
      </div>
      :
      `${item.start_time} ~ ${item.end_time}`;
  }, [job_id, dateType]);

  // 判断按钮是否应该高亮
  const isDateButtonActive = useCallback((item: DateItem) => {
    return job_id === 'market_report_month'
      ? dayjs(selectedDate).isSame(dayjs(item.start_time), 'month')
      : item.start_time === selectedDate;
  }, [job_id, selectedDate]);

  const currentVerIsDefault = versionState.current === versionState.default;

  return (
    <div className={styles.container}>
      <div className={styles.dateNavigatorWrapper}>
        <div className={styles.dateNavigatorContainer}>
          {showNavButtons && (
            <Button icon={<LeftOutlined />} type="text" onClick={handlePrev} size="small" />
          )}
          <div className={styles.dateButtonsWrapper} ref={scrollWrapperRef}>
            <Space size="middle">
              {dateItems.map((item) => (
                <Button
                  key={item.start_time}
                  type={isDateButtonActive(item) ? 'primary' : 'default'}
                  onClick={() => isDateButtonActive(item)? undefined : handleDateClick(item.start_time)}
                  size='small'
                  style={{cursor: isDateButtonActive(item)? 'default' : 'pointer'}}
                  className={styles.dateButton}
                >
                  {formatDateDisplay(item)}
                </Button>
              ))}
            </Space>
          </div>
          {showNavButtons && (
            <Button icon={<RightOutlined />} type="text" onClick={handleNext} size="small" />
          )}
        </div>
      </div>
      {/* 修订历史部分 */}
      {selectedDateItem?.ver_list && selectedDateItem.ver_list.length > 1 && (
        <Space size={16}>
          <span className={styles.revisionHistory}>
            <span className={styles.revisionHistoryLabel}>修订历史： </span>
            <Select
              value={versionState.current}
              onChange={handleVersionChange}
              options={selectedDateItem.ver_list.map(item => ({
                label: `V${item.ver}`,
                value: item.ver,
              }))}
              style={{ width: 65 }}
              size='small'
            />
          </span>
          <Tooltip title={currentVerIsDefault ? null : '将当前版本设置为默认投放策略'}>
            <Checkbox
              disabled={currentVerIsDefault}
              onClick={() => handleSetDefaultVersion(versionState.current)}
              checked={currentVerIsDefault}
            >
              {currentVerIsDefault ? '已默认' : '设为默认'}
            </Checkbox>
          </Tooltip>
        </Space>
      )}
    </div>
  );
};

export default DateNavigator;
