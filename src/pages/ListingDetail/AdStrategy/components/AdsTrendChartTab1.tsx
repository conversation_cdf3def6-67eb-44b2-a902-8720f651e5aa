import React, { useEffect, useRef, useMemo } from 'react';
import * as echarts from 'echarts';
import dayjs from 'dayjs';
import 'dayjs/locale/en';
import { getWeekday } from '@/utils/bus';
import { chartColors } from '@/utils/common';

type WeekAdsTrendChartProps = {
  currency: string;
  data: {
    xAxisData: string[];
    spend: number[];
    sales: number[];
    acos: number[];
    cvr: number[];
    orders: number[];
    clicks: number[];
    impressions: number[];
    ctr: number[];
    cpc: number[];
    sku_sales: number[];
    cpa: number[];
    spend_totle?: number[];
    sales_totle?: number[];
    cvr_totle?: string[];
    acos_totle?: string[];
    orders_totle?: number[];
    clicks_totle?: number[];
    expected_spend?: number[];
    target_sales?: number[];
    target_acos?: number[];
    target_cvr?: number[];
  };
  showTotal?: boolean;
  chartOption?: {
    axisLabel: any
  }
  hideLegend?: boolean;
  dateType: 'month' | 'week' | 'day';
  metricsOrder: string[];
};

const containerStyle6 = `display: grid; grid-template-columns: auto auto 1fr auto auto 1fr; align-items: center; grid-gap: 4px 8px; color: #fff;`;
const containerStyle3 = `display: grid; grid-template-columns: auto auto 1fr; align-items: center; grid-gap: 4px 8px; color: #fff;`;

/** 周或者日的广告趋势图 */
const AdsTrendChart: React.FC<WeekAdsTrendChartProps> = (props) => {
  const { currency, data, showTotal = false, dateType, metricsOrder } = props;
  const {
    xAxisData = [],
    spend = [],
    sales = [],
    acos = [],
    cvr = [],
    orders = [],
    clicks = [],
    impressions = [],
    ctr = [],
    cpc = [],
    sku_sales = [],
    cpa = [],
    expected_spend = [],
    target_sales = [],
    target_acos = [],
    target_cvr = [],
  } = data;
  const chartRef = useRef<HTMLDivElement>(null);

  // 指标配置映射
  const METRIC_CONFIG = {
    sales: { name: '广告销售额', type: 'bar', yAxisIndex: 0, data: sales, format: 'currency' },
    spend: { name: '广告花费', type: 'bar', yAxisIndex: 0, data: spend, format: 'currency' },
    acos: { name: 'ACOS', type: 'line', yAxisIndex: 1, data: acos, format: 'percentage' },
    cvr: { name: 'CVR', type: 'line', yAxisIndex: 1, data: cvr, format: 'percentage' },
    orders: { name: '订单量', type: 'bar', yAxisIndex: 0, data: orders, format: 'number' },
    clicks: { name: '点击量', type: 'bar', yAxisIndex: 0, data: clicks, format: 'number' },
    impressions: { name: '曝光量', type: 'bar', yAxisIndex: 0, data: impressions, format: 'number' },
    ctr: { name: 'CTR', type: 'line', yAxisIndex: 1, data: ctr, format: 'percentage' },
    cpc: { name: 'CPC', type: 'line', yAxisIndex: 0, data: cpc, format: 'currency' },
    sku_sales: { name: '直接成交销售额', type: 'bar', yAxisIndex: 0, data: sku_sales, format: 'currency' },
    cpa: { name: 'CPA', type: 'line', yAxisIndex: 0, data: cpa, format: 'currency' },
  };

  // 获取有数据的指标
  const activeMetrics = useMemo(() => {
    return metricsOrder
      .map(key => {
        const config = METRIC_CONFIG[key as keyof typeof METRIC_CONFIG];
        if (config && config.data && config.data.length > 0) {
          return [key, config];
        }
        return null;
      })
      .filter(Boolean) as [string, any][];
  }, [metricsOrder, data]);

  // 格式化数值的函数
  const formatValue = (value: number, format: string) => {
    if (value === null || value === undefined) return '-';

    switch (format) {
      case 'currency':
        return `${currency}${value.toFixed(2)}`;
      case 'percentage':
        return `${value.toFixed(2)}%`;
      case 'number':
        return value.toString();
      default:
        return value.toString();
    }
  };

  useEffect(() => {
    if (chartRef.current) {
      const myChart = echarts.init(chartRef.current);

      const option: echarts.EChartsOption = {
        color: chartColors,
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0,0,0,0.65)', // 黑色背景
          borderColor: '#444',
          textStyle: {
            color: '#fff', // 白色文字
          },
          formatter: (params: any) => {
            const { dataIndex } = params[0];

            // 处理日期显示，当 dateType 为 day 时添加周几
            let dateDisplay = params[0].axisValue;
            if (dateType === 'day') {
              const date = dayjs(params[0].axisValue);
              const weekdayEng = date.locale('en').format('ddd');
              const weekday = getWeekday(weekdayEng);
              if (weekday) {
                dateDisplay = `${params[0].axisValue} ${weekday}`;
              }
            }

            let result = `<div style="color:#fff; margin-bottom: 8px;">${dateDisplay}</div>`;
            let gridContent = '';

            const seriesData: Record<string, any> = {};
            params.forEach((p: any) => {
              seriesData[p.seriesName] = p;
            });

            const targetDataMap: Record<string, { label: string; value: number; format: string }> = {
              spend: { label: '预期花费', value: expected_spend?.[dataIndex], format: 'currency' },
              sales: { label: '目标销售', value: target_sales?.[dataIndex], format: 'currency' },
              acos: { label: '目标ACoS', value: target_acos?.[dataIndex], format: 'percentage' },
              cvr: { label: '目标CVR', value: target_cvr?.[dataIndex], format: 'percentage' },
            };

            let noRightContent = true

            // 使用动态的指标顺序
            activeMetrics.forEach(([key, config]) => {
              const param = seriesData[config.name];
              if (!param) return;

              const { value } = param;
              const formattedValue = formatValue(value, config.format);

              const target = targetDataMap[key];
              if (target && target.value !== null && target.value !== undefined && !isNaN(target.value)) {
                noRightContent = false
                const formattedTargetValue = formatValue(target.value, target.format);
                gridContent += `
                      <div>${param.marker} ${config.name}</div>
                      <div>:</div>
                      <div style="text-align: right; font-weight: bold;">${formattedValue}</div>
                      <div style="margin-left: 16px;">${target.label}</div>
                      <div>:</div>
                      <div style="text-align: right; font-weight: bold;">${formattedTargetValue}</div>
                  `;
              } else {
                gridContent += `
                      <div>${param.marker} ${config.name}</div>
                      <div>:</div>
                      <div style="text-align: right; font-weight: bold;">${formattedValue}</div>
                      <div></div>
                      <div></div>
                      <div></div>
                  `;
              }
            });

            result += `<div style="${noRightContent ? containerStyle3 : containerStyle6}">${gridContent}</div>`;
            return result;
          }
        },
        legend: {
          data: activeMetrics.map(([, config]) => config.name),
          show: !props.hideLegend,
          right: '2%',
          top: 'top',
        },
        grid: {
          left: '2%',
          right: '2%',
          bottom: '1%',
          top: 60,
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: true,
          data: xAxisData,
          axisLabel: {
            ...props.chartOption?.axisLabel,
            hideOverlap: true,
            // 1. 使用 rich 来定义一个富文本样式
            rich: {
              // 'a' 是一个自定义的样式名，你可以随意命名
              a: {
                // 在这里直接设置行高
                lineHeight: 16
              },
              weekday: {
                color: '#1890ff',
                fontSize: 12,
                lineHeight: 14
              },
              weekend: {
                color: '#fa8c16',
                fontSize: 12,
                lineHeight: 14
              }
            },
            // 2. 使用 formatter 将上面定义的样式应用到每个标签上
            formatter: (value: string) => {
              if (dateType === 'day') {
                const date = dayjs(value);
                const weekdayEng = date.locale('en').format('ddd');
                const weekday = getWeekday(weekdayEng);
                if (weekday) {
                  // 判断是否为周末（周六、周日）
                  const isWeekend = weekdayEng === 'Sat' || weekdayEng === 'Sun';
                  const weekdayStyle = isWeekend ? 'weekend' : 'weekday';
                  return `{a|${value}}\n{${weekdayStyle}|${weekday}}`;
                }
              }
              return `{a|${value}}`;
            }
          }
        },
        yAxis: [
          {
            type: 'value',
            position: 'left',
            axisLabel: {
              formatter: '{value}',
            },
          },
          {
            type: 'value',
            position: 'right',
            axisLabel: {
              formatter: '{value} %',
            },
            splitLine: {
              show: false, // 隐藏右侧y轴的横向指示线
            },
          },
        ],
        series: activeMetrics.map(([, config]) => {
          const seriesConfig: any = {
            name: config.name,
            type: config.type,
            yAxisIndex: config.yAxisIndex,
            data: config.data,
          };

          if (config.type === 'bar') {
            seriesConfig.barMaxWidth = 30;
          } else if (config.type === 'line') {
            seriesConfig.showSymbol = false;
            seriesConfig.lineStyle = {
              width: 2,
            };
          }

          return seriesConfig;
        }),
      };

      myChart.setOption(option);

      const resizeHandler = () => {
        myChart.resize();
      };
      window.addEventListener('resize', resizeHandler);

      return () => {
        window.removeEventListener('resize', resizeHandler);
        myChart.dispose();
      };
    }
  }, [currency, data, showTotal, props.chartOption, props.hideLegend, dateType, activeMetrics]);

  return <div ref={chartRef} style={{ width: '100%', height: '100%' }} />;
};

export default AdsTrendChart;