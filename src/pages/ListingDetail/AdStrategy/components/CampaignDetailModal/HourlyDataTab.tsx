import React, { useState, useEffect, useMemo, useRef } from 'react';
import { Table, Space, Row, Col, Spin, TableColumnType, Empty, Select, Typography } from 'antd';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import { useModel } from '@umijs/max';
import ReusableChart from './ReusableChart';
import { getCampaignHour } from '@/services/ibidder_api/operation';
import type { HourData, HourTotalData } from './types';
import { formatCurrency, countryTimezoneMap } from '@/utils/bus';
import { genericSorter } from '@/utils/common';
import PresetRangePicker from './components/PresetRangePicker';
import { createTitleWithTooltip } from './utils/tooltipConfig';
const { Text } = Typography;

const hourlyDataSeriesConfig = [
  { name: '广告订单', key: 'orders', type: 'bar' as const, yAxisIndex: 0, barMaxWidth: 30 },
  { name: '花费', key: 'spend', type: 'bar' as const, yAxisIndex: 0, barMaxWidth: 30 },
  { name: '销售额', key: 'sales', type: 'bar' as const, yAxisIndex: 0, barMaxWidth: 30 },
  { name: 'ACoS', key: 'acos', type: 'line' as const, yAxisIndex: 1, showSymbol: false },
  { name: 'CVR', key: 'cvr', type: 'line' as const, yAxisIndex: 1, showSymbol: false },
  { name: 'CTR', key: 'ctr', type: 'line' as const, yAxisIndex: 1, showSymbol: false },
  { name: '曝光量', key: 'impressions', type: 'line' as const, yAxisIndex: 0, showSymbol: false },
  { name: '点击', key: 'clicks', type: 'line' as const, yAxisIndex: 0, showSymbol: false },
  { name: '点击百分比', key: 'click_ratio', type: 'line' as const, yAxisIndex: 1, showSymbol: false },
  { name: 'CPC', key: 'cpc', type: 'line' as const, yAxisIndex: 0, showSymbol: false },
  { name: 'CPA', key: 'cpa', type: 'line' as const, yAxisIndex: 0, showSymbol: false },
  { name: '广告销量', key: 'units', type: 'bar' as const, yAxisIndex: 0, barMaxWidth: 30 },
  { name: '直接成交订单', key: 'sku_orders', type: 'bar' as const, yAxisIndex: 0, barMaxWidth: 30 },
  { name: '直接成交销售额', key: 'sku_sales', type: 'bar' as const, yAxisIndex: 0, barMaxWidth: 30 },
  { name: '花费百分比', key: 'spend_ratio', type: 'line' as const, yAxisIndex: 1, showSymbol: false },
  { name: '销售额百分比', key: 'sales_ratio', type: 'line' as const, yAxisIndex: 1, showSymbol: false },
];

const getHourlyTableColumns = (currency: string): TableColumnType<HourData>[] => [
  {
    title: '小时',
    width: 128,
    fixed: 'left' as const,
    dataIndex: 'hour',
    key: 'hour',
    render: (hour: number) => `${String(hour).padStart(2, '0')}:00 - ${String(hour).padStart(2, '0')}:59`,
    sorter: (a, b) => genericSorter(a, b, 'hour'),
    defaultSortOrder: 'ascend' as const,
  },
  {
    title: createTitleWithTooltip('广告订单', 'orders'),
    width: 128,
    dataIndex: 'orders',
    key: 'orders',
    sorter: (a, b) => genericSorter(a, b, 'orders'),
    align: 'right',
  },
  {
    title: createTitleWithTooltip('花费', 'spend'),
    width: 128,
    dataIndex: 'spend',
    key: 'spend',
    render: (value: any) => formatCurrency(value, currency),
    sorter: (a, b) => genericSorter(a, b, 'spend'),
    align: 'right',
  },
  {
    title: createTitleWithTooltip('销售额', 'sales'),
    width: 138,
    dataIndex: 'sales',
    key: 'sales',
    render: (value: any) => formatCurrency(value, currency),
    sorter: (a, b) => genericSorter(a, b, 'sales'),
    align: 'right',
  },
  {
    title: createTitleWithTooltip('ACoS', 'acos'),
    width: 118,
    dataIndex: 'acos',
    key: 'acos',
    sorter: (a, b) => genericSorter(a, b, 'acos'),
    align: 'right',
  },
  {
    title: createTitleWithTooltip('CVR', 'cvr'),
    width: 118,
    dataIndex: 'cvr',
    key: 'cvr',
    sorter: (a, b) => genericSorter(a, b, 'cvr'),
    align: 'right',
  },
  {
    title: createTitleWithTooltip('CTR', 'ctr'),
    width: 118,
    dataIndex: 'ctr',
    key: 'ctr',
    sorter: (a, b) => genericSorter(a, b, 'ctr'),
    align: 'right',
  },
  {
    title: createTitleWithTooltip('曝光量', 'impressions'),
    width: 118,
    dataIndex: 'impressions',
    key: 'impressions',
    sorter: (a, b) => genericSorter(a, b, 'impressions'),
    align: 'right',
  },
  {
    title: createTitleWithTooltip('点击', 'clicks'),
    width: 118,
    dataIndex: 'clicks',
    key: 'clicks',
    sorter: (a, b) => genericSorter(a, b, 'clicks'),
    align: 'right',
  },
  {
    title: createTitleWithTooltip('点击百分比', 'click_ratio'),
    width: 143,
    dataIndex: 'click_ratio',
    key: 'click_ratio',
    sorter: (a, b) => genericSorter(a, b, 'click_ratio'),
    align: 'right',
  },
  {
    title: createTitleWithTooltip('CPC', 'cpc'),
    width: 118,
    dataIndex: 'cpc',
    key: 'cpc',
    render: (value: any) => formatCurrency(value, currency),
    sorter: (a, b) => genericSorter(a, b, 'cpc'),
    align: 'right',
  },
  {
    title: createTitleWithTooltip('CPA', 'cpa'),
    width: 118,
    dataIndex: 'cpa',
    key: 'cpa',
    render: (value: any) => formatCurrency(value, currency),
    sorter: (a, b) => genericSorter(a, b, 'cpa'),
    align: 'right',
  },
  {
    title: createTitleWithTooltip('广告销量', 'units'),
    width: 128,
    dataIndex: 'units',
    key: 'units',
    sorter: (a, b) => genericSorter(a, b, 'units'),
    align: 'right',
  },
  {
    title: createTitleWithTooltip('花费百分比', 'spend_ratio'),
    width: 138,
    dataIndex: 'spend_ratio',
    key: 'spend_ratio',
    sorter: (a, b) => genericSorter(a, b, 'spend_ratio'),
    align: 'right',
  },
  {
    title: createTitleWithTooltip('销售额百分比', 'sales_ratio'),
    width: 158,
    dataIndex: 'sales_ratio',
    key: 'sales_ratio',
    sorter: (a, b) => genericSorter(a, b, 'sales_ratio'),
    align: 'right',
  },
  {
    title: createTitleWithTooltip('直接成交订单', 'sku_orders'),
    width: 158,
    dataIndex: 'sku_orders',
    key: 'sku_orders',
    sorter: (a, b) => genericSorter(a, b, 'sku_orders'),
    align: 'right',
  },
  {
    title: createTitleWithTooltip('直接成交销售额', 'sku_sales'),
    width: 168,
    dataIndex: 'sku_sales',
    key: 'sku_sales',
    render: (value: any) => formatCurrency(value, currency),
    sorter: (a, b) => genericSorter(a, b, 'sku_sales'),
    align: 'right',
  },
];

interface HourlyDataTabProps {
  campaignId?: number;
  campaign_type?: string;
}

const HourlyDataTab: React.FC<HourlyDataTabProps> = ({ campaignId, campaign_type }) => {
  const { productInfo } = useModel('productInfo');
  const profileId = productInfo?.profile_id;
  const currency = productInfo?.currency || '';
  const country = (productInfo?.country || '').toLowerCase();
  const tz = countryTimezoneMap[country as keyof typeof countryTimezoneMap];
  const now = tz ? dayjs().tz(tz) : dayjs();
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const [chartKey, setChartKey] = useState(0);

  const [tableData, setTableData] = useState<HourData[]>([]);
  const [totalData, setTotalData] = useState<HourTotalData | null>(null);
  const [loading, setLoading] = useState(false);
  const [dateRange, setDateRange] = useState<[string, string]>([
    now.clone().subtract(6, 'days').format('YYYY-MM-DD'),
    now.clone().format('YYYY-MM-DD'),
  ]);
  const [dateRangeValue, setDateRangeValue] = useState<[Dayjs, Dayjs]>([
    now.clone().subtract(6, 'days'),
    now.clone(),
  ]);
  const [selectedSeries, setSelectedSeries] = useState<string[]>(() =>
    hourlyDataSeriesConfig.slice(0, 6).map((s) => s.name),
  );
  const [selectedDay, setSelectedDay] = useState<number | null>(null);

  useEffect(() => {
    const handleVisibilityChange = () => {
      if (chartContainerRef.current) {
        const observer = new IntersectionObserver((entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting && entry.intersectionRatio > 0) {
              setTimeout(() => {
                setChartKey((prev) => prev + 1);
              }, 50);
            }
          });
        });

        observer.observe(chartContainerRef.current);
        return () => observer.disconnect();
      }
    };

    return handleVisibilityChange();
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      if (!campaignId || !profileId || !campaign_type) {
        return;
      }

      setLoading(true);
      try {
        const response = await getCampaignHour({
          campaign_id: campaignId,
          profile_id: profileId,
          campaign_type: campaign_type as 'sp' | 'sd',
          start_date: dateRange[0],
          end_date: dateRange[1],
          week_day: selectedDay
        });
        setTableData(response.data?.listing || []);
        setTotalData(response.data?.sum || null);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [campaignId, profileId, dateRange, campaign_type, selectedDay]);

  const chartData = useMemo(() => {
    if (!tableData || tableData.length === 0) {
      return null;
    }

    const sortedData = [...tableData].sort((a, b) => a.hour - b.hour);

    return {
      dates: sortedData.map((d) => `${String(d.hour).padStart(2, '0')}:00`),
      spend: sortedData.map((d) => d.spend),
      sales: sortedData.map((d) => d.sales),
      orders: sortedData.map((d) => d.orders),
      acos: sortedData.map((d) => parseFloat(d.acos.replace('%', ''))),
      cvr: sortedData.map((d) => parseFloat(d.cvr.replace('%', ''))),
      ctr: sortedData.map((d) => parseFloat(d.ctr.replace('%', ''))),
      impressions: sortedData.map((d) => d.impressions),
      clicks: sortedData.map((d) => d.clicks),
      cpc: sortedData.map((d) => d.cpc),
      units: sortedData.map((d) => d.units),
      click_ratio: sortedData.map((d) => parseFloat(d.click_ratio.replace('%', ''))),
      cpa: sortedData.map((d) => d.cpa),
      spend_ratio: sortedData.map((d) => parseFloat(d.spend_ratio.replace('%', ''))),
      sales_ratio: sortedData.map((d) => parseFloat(d.sales_ratio.replace('%', ''))),
      sku_orders: sortedData.map((d) => d.sku_orders),
      sku_sales: sortedData.map((d) => d.sku_sales),
    };
  }, [tableData]);

  const handleDateChange = (
    dates: null | (Dayjs | null)[] | any,
    dateStrings: [string, string],
  ) => {
    if (dates && dates[0] && dates[1] && dateStrings[0] && dateStrings[1]) {
      setDateRangeValue([dates[0], dates[1]]);
      setDateRange(dateStrings);
    }
  };

  const handleSelectedSeriesChange = (newSelectedSeries: string[]) => {
    setSelectedSeries(newSelectedSeries);
  };

  const handleSelectedDayChange = (value: number) => {
    setSelectedDay(value);
  };

  return (
    <Spin spinning={loading}>
      <div>
        <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
          <Col>
            <Space>
              <PresetRangePicker value={dateRangeValue} onChange={handleDateChange} maxDateRange={60} presetCount={8} />
              <Select
                style={{ width: 100 }}
                value={selectedDay}
                allowClear
                onChange={handleSelectedDayChange}
                options={[
                  { label: '全部', value: null },
                  { label: '星期一', value: 0 },
                  { label: '星期二', value: 1 },
                  { label: '星期三', value: 2 },
                  { label: '星期四', value: 3 },
                  { label: '星期五', value: 4 },
                  { label: '星期六', value: 5 },
                  { label: '星期日', value: 6 },
                ]}
              />
              <Text type="danger" style={{ fontSize: 12 }}>*小时数据来源于Amazon Marketing Stream，只提供授权后的；因亚马逊会修正数据，小时和天数据可能会存在差异。</Text>
            </Space>
          </Col>
        </Row>

        <div ref={chartContainerRef} style={{ height: '300px', width: '100%' }}>
          {chartData && tableData.length > 0 ? (
            <ReusableChart
              key={chartKey}
              currency={currency}
              dates={chartData.dates}
              series={hourlyDataSeriesConfig.map((config) => ({
                ...config,
                data: chartData[config.key as keyof typeof chartData],
              }))}
              currencyFormattedSeries={['花费', '销售额', 'CPC', 'CPA', '直接成交销售额']}
              percentageFormattedSeries={['ACoS', 'CVR', 'CTR', '点击百分比', '花费百分比', '销售额百分比']}
              selectedSeries={selectedSeries}
              onSelectedSeriesChange={handleSelectedSeriesChange}
            />
          ) : (
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
          )}
        </div>

        <Table<HourData>
          sticky
          style={{ marginTop: 32 }}
          columns={getHourlyTableColumns(currency)}
          dataSource={tableData}
          pagination={false}
          scroll={{ x: 'max-content' }}
          summary={() => {
            if (!totalData) return null;
            return (
              <Table.Summary fixed="top">
                <Table.Summary.Row>
                  <Table.Summary.Cell index={0}>汇总</Table.Summary.Cell>
                  <Table.Summary.Cell index={1} align="right">{totalData.orders}</Table.Summary.Cell>
                  <Table.Summary.Cell index={2} align="right">{formatCurrency(totalData.spend, currency)}</Table.Summary.Cell>
                  <Table.Summary.Cell index={3} align="right">{formatCurrency(totalData.sales, currency)}</Table.Summary.Cell>
                  <Table.Summary.Cell index={4} align="right">{totalData.acos}</Table.Summary.Cell>
                  <Table.Summary.Cell index={5} align="right">{totalData.cvr}</Table.Summary.Cell>
                  <Table.Summary.Cell index={6} align="right">{totalData.ctr}</Table.Summary.Cell>
                  <Table.Summary.Cell index={7} align="right">{totalData.impressions}</Table.Summary.Cell>
                  <Table.Summary.Cell index={8} align="right">{totalData.clicks}</Table.Summary.Cell>
                  <Table.Summary.Cell index={9} align="right">-</Table.Summary.Cell>
                  <Table.Summary.Cell index={10} align="right">{formatCurrency(totalData.cpc, currency)}</Table.Summary.Cell>
                  <Table.Summary.Cell index={11} align="right">{formatCurrency(totalData.cpa, currency)}</Table.Summary.Cell>
                  <Table.Summary.Cell index={12} align="right">{totalData.units}</Table.Summary.Cell>
                  <Table.Summary.Cell index={13} align="right">-</Table.Summary.Cell>
                  <Table.Summary.Cell index={14} align="right">-</Table.Summary.Cell>
                  <Table.Summary.Cell index={15} align="right">{totalData.sku_orders}</Table.Summary.Cell>
                  <Table.Summary.Cell index={16} align="right">{formatCurrency(totalData.sku_sales, currency)}</Table.Summary.Cell>
                </Table.Summary.Row>
              </Table.Summary>
            );
          }}
        />
      </div>
    </Spin>
  );
};

export default HourlyDataTab;
