import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Typography, Statistic, Table, Tag, Space, Flex, message, Button } from 'antd';
import { useModel, useSearchParams } from '@umijs/max';
import dayjs from 'dayjs';
import ExpectedResults from '../../Common/ExpectedResults';
import { getBaseReport, getAdStrategy, editReport, approveReport, getRoleAgentDetail } from '@/services/ibidder_api/operation';
import { normalizeDataRecursively, weekStrategyConfig, realAdsResultConfig } from '@/utils/dataTransformer';
import WeekAnalysisCard from '../../Common/WeekAnalysisCard';
import AdsTrendCard from '../../Common/AdsTrendCard';
import RevisionHistory from './RevisionHistory';
import ConfirmationSection from './ConfirmationSection';
import FeedbackButton from './FeedbackButton';
import DateNavigator from './DateNavigator';
import EmptyState from '../../Common/EmptyState';
import ReportFailureState from '../../Common/ReportFailureState';
import GeneratingState from '../../Common/GeneratingState';
import { WeekStrategyModalData } from '@/models/globalModals';
import { getCountryTimezone } from '@/utils/bus';
import { Loading } from '@/components';
import { EditOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;

// 每日策略数据类型（从 Strategy.WeekStrategyData.daily_strategy_suggestion 派生）
type DailyStrategyItem = {
  date: string;
  approach: string;
  guideline: string;
  budget_range: { min: number; max: number };
  bid_adjustment_range: { min: number; max: number };
};

export interface WeekStrategyContentProps extends WeekStrategyModalData {
  onTitleChange?: (title: string) => void;
  onCancel?: (refresh: boolean) => void;
  onSuccess: () => void;
  showControlBtn?: boolean;
  /** 审核区域是否显示 看板上不显示 */
  showReviewArea?: boolean;
}

const WeekStrategyContent: React.FC<WeekStrategyContentProps> = (props) => {
  const {
    date,
    job_id,
    target_job_id,
    isCompleteStrategy = false,
    onTitleChange,
    onCancel = () => { },
    onSuccess,
    showControlBtn,
    showReviewArea = true
  } = props

  // 全局状态管理
  const { updateAlertFn } = useModel('updateAlert');
  const { openWeekStrategyModal } = useModel('globalModals');
  const [searchParams] = useSearchParams();
  const parent_asin = searchParams.get('asin') as string;
  const profile_id = searchParams.get('profile_id') as string;
  const { productInfo } = useModel('productInfo');
  const currency = productInfo?.currency || '$';
  const country = productInfo?.country || '';
  const [ajaxData, setAjaxData] = useState<AjaxData.WeekStrategyData | null>(null)
  const [aiFeedbackContent, setAiFeedbackContent] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [isFirstDate, setIsFirstDate] = useState(false);

  // 获取周策略数据
  const fetchWeekStrategyData = async () => {
    try {
      setLoading(true);
      // 初始加载时默认认为是第一个日期
      setIsFirstDate(true);

      if (isCompleteStrategy) {
        // 从"查看本周完整策略"按钮进入，使用 getWeekStrategy 接口
        const res: any = await getAdStrategy({
          date: date || getCountryTimezone(country, 'YYYY-MM-DD'),
          asin: parent_asin,
          profile_id,
          job_id: target_job_id || job_id
        });
        setLoading(false);

        if (res.code === 200 && res.data) {
          setAjaxData(res.data);

          if (res.data.result && res.data.result.ads_strategy_week) {

            // 设置弹框标题
            const normalizedData = normalizeDataRecursively<Strategy.WeekStrategyData>(
              res.data.result.ads_strategy_week,
              weekStrategyConfig
            );

            if (onTitleChange && normalizedData) {
              const { start_date, end_date } = normalizedData;
              const title = `周广告投放策略（${start_date} ~ ${end_date}）`;
              onTitleChange(title);
            }
          }

          return;
        }
      } else if (parent_asin && profile_id && job_id && props.current_time) {
        // 其他情况使用 getBaseReport 接口
        const res: any = await getBaseReport({
          job_id,
          asin: parent_asin,
          profile_id: profile_id.toString(),
          current_time: props.current_time,
          target_job_id
        });
        setLoading(false);

        if (res.data) {
          setAjaxData(res.data);

          if (res.data.result && res.data.result.ads_strategy_week) {
            // 设置弹框标题
            if (onTitleChange && res.data.result.ads_strategy_week) {
              const { start_date, end_date } = res.data.result.ads_strategy_week;
              const title = `周广告投放策略（${start_date} ~ ${end_date}）`;
              onTitleChange(title);
            }
          }

          return;
        }
      }
    } catch (error) {
      console.error('获取周策略数据失败:', error);
      message.error('获取数据失败，请重试');
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchWeekStrategyData();
  }, [parent_asin, profile_id, date, isCompleteStrategy, job_id, props.current_time]);

  // 统一的 ajaxData 异常判断，如果没有数据就直接返回
  if (!ajaxData) {
    return null
  }

  // 从 ajaxData 中派生的计算属性
  const current_time = ajaxData.current_time;
  const weekStrategyData = ajaxData.result && ajaxData.result.ads_strategy_week
    ? normalizeDataRecursively<Strategy.WeekStrategyData>(ajaxData.result.ads_strategy_week, weekStrategyConfig)
    : null;
  const _real_ads_result = ajaxData.result && ajaxData.result.real_ads_result
    ? normalizeDataRecursively<Strategy.Real_ads_result>(ajaxData.result.real_ads_result, realAdsResultConfig)
    : undefined;



  // 处理周导航器数据更新的回调函数
  const handleStrategyDataUpdate = (data: AjaxData.WeekStrategyData | null) => {
    if (data) {
      setAjaxData(data);
      if (data.result && data.result.ads_strategy_week) {
        // 设置弹框标题
        const normalizedData = normalizeDataRecursively<Strategy.WeekStrategyData>(
          data.result.ads_strategy_week,
          weekStrategyConfig
        );

        if (onTitleChange && normalizedData) {
          const { start_date, end_date } = normalizedData;
          const title = `周广告投放策略（${start_date} ~ ${end_date}）`;
          onTitleChange(title);
        }
      }
    } else {
      setAjaxData(null);
    }
  };

  // DateNavigator的日期变更回调
  const handleDateChange = async (apiDate: string, isFirst: boolean = false) => {
    setIsFirstDate(isFirst);
    setLoading(true);
    try {
      const res: any = await getAdStrategy({
        date: apiDate,
        asin: parent_asin,
        profile_id,
        job_id: target_job_id || job_id
      });
      setLoading(false);

      if (res.code === 200 && res.data) {
        handleStrategyDataUpdate(res.data);
      } else {
        handleStrategyDataUpdate(null);
      }
    } catch (error) {
      setLoading(false);
      handleStrategyDataUpdate(null);
    }
  };

  // DateNavigator的版本变更回调
  const handleVersionChange = async (targetEsId: string) => {
    setLoading(true);
    try {
      const res: any = await getRoleAgentDetail({ es_id: targetEsId });
      setLoading(false);

      if (res.code === 200) {
        handleStrategyDataUpdate(res.data);
      } else {
        // message.error('获取版本数据失败');
      }
    } catch (error) {
      setLoading(false);
      // message.error('获取版本数据失败，请重试');
    }
  };

  // 转换每日预算数据为表格所需格式
  const getDailyStrategyData = () => {
    if (!weekStrategyData) return []
    const dailyData: DailyStrategyItem[] = [];

    Object.keys(weekStrategyData.daily_strategy_suggestion).forEach((date) => {
      const data = weekStrategyData.daily_strategy_suggestion[date];
      dailyData.push({
        date,
        ...data
      });
    });

    // 按日期升序排列
    dailyData.sort((a, b) => a.date.localeCompare(b.date));

    return dailyData;
  };

  const dailyStrategyData = getDailyStrategyData();



  // 审核方法
  const handleApprove = async () => {
    approveReport({
      profile_id: profile_id,
      es_id: ajaxData.es_id,
      job_id: ajaxData.job_id,
      target_week: ajaxData.target_week,
      parent_asin: parent_asin,
    }).then((res) => {
      if (res.code === 200) {
        // 更新全局状态
        updateAlertFn(true);
        onSuccess();
        message.success('审核已成功提交');
        if (onCancel) onCancel(true);
      } else {
        message.error(res?.message || '审核失败，请重试');
      }
    }).catch((error) => {
      console.error('审核失败:', error);
    });
  };

  // 修改方法
  const handleModify = async () => {
    if (!current_time || !parent_asin || !profile_id) {
      console.error("缺少必要的参数，无法提交");
      return;
    }

    // 如果 report_status 是 false，需要先审核
    if (!ajaxData.report_status) {
      try {
        // 先调用审核接口
        await approveReport({
          profile_id: profile_id,
          es_id: ajaxData.es_id,
          job_id: ajaxData.job_id,
          target_week: ajaxData.target_week,
          parent_asin: parent_asin,
        });
      } catch (error) {
        console.error('审核失败:', error);
      }
    }

    // 如果没有AI反馈内容
    if (!aiFeedbackContent.trim()) {
      return;
    }

    // 创建一个深拷贝的数据对象
    const updatedWeekStrategyData = weekStrategyData ? JSON.parse(JSON.stringify(weekStrategyData)) : {};

    // 如果有AI反馈内容，保存到数据对象中
    if (aiFeedbackContent.trim()) {
      updatedWeekStrategyData.ai_feedbackContent = aiFeedbackContent.trim();
    }

    const payload = {
      es_id: ajaxData.es_id,
      current_time: current_time,
      parent_asin: parent_asin,
      asins: ajaxData.asins,
      role: ajaxData.role,
      job_id: ajaxData.job_id,
      profile_id: profile_id,
      country: country,
      data: {
        ads_strategy_week: updatedWeekStrategyData,
      },
    };
    try {
      const res = await editReport(payload);
      if (res && res.code === 200) {
        message.success('修改已成功提交');
        if (onCancel) onCancel(true); // 只有成功时才关闭弹框
        // 更新全局状态
        updateAlertFn(true);
        onSuccess();
      } else {
        message.error(res?.message || '修改提交失败');
      }
    } catch (error) {
      console.error('修改失败:', error);
    }
  };

  // 每日预算表格列配置
  const dayBudgetColumns = [
    {
      title: '日期',
      dataIndex: 'date',
      key: 'date',
      width: '15%',
      render: (text: string) => {
        // 使用dayjs解析日期并获取星期几
        const weekdayMap = ['日', '一', '二', '三', '四', '五', '六'];
        const weekday = weekdayMap[dayjs(text).day()];
        return `${text}（周${weekday}）`;
      }
    },
    {
      title: '策略方法',
      dataIndex: 'approach',
      key: 'approach',
      width: '10%',
      render: (text: string) => {
        let color = 'blue';
        if (text === 'aggressive') {
          color = 'red';
        } else if (text === 'conservative') {
          color = 'green';
        }
        return (
          <Tag color={color}>
            {text === 'balanced' ? '平衡' :
              text === 'aggressive' ? '激进' :
                text === 'conservative' ? '保守' : text}
          </Tag>
        );
      }
    },
    {
      title: '预算范围',
      key: 'budget',
      width: '15%',
      render: (_: any, record: DailyStrategyItem) => (
        <Text strong>{currency}{record.budget_range.min} ~ {currency}{record.budget_range.max}</Text>
      )
    },
    {
      title: '竞价调整范围',
      key: 'bid_adjustment',
      width: '18%',
      render: (_: any, record: DailyStrategyItem) => {
        const minValue = record.bid_adjustment_range.min * 100;
        const maxValue = record.bid_adjustment_range.max * 100;
        let minColor = 'blue';
        if (minValue > 0) {
          minColor = 'red';
        } else if (minValue < 0) {
          minColor = 'green'
        }
        let maxColor = 'blue';
        if (maxValue > 0) {
          maxColor = 'red';
        } else if (maxValue < 0) {
          maxColor = 'green'
        }
        return (
          <Space>
            <Tag color={minColor}>{minValue}%</Tag>
            <Text>至</Text>
            <Tag color={maxColor}>{maxValue}%</Tag>
          </Space>
        );
      }
    },
    {
      title: '调整理由',
      dataIndex: 'guideline',
      key: 'guideline',
      // width: '45%',
      render: (text: string) => (
        <Text style={{ fontSize: '14px' }}>{text}</Text>
      )
    }
  ];

  let contentCards;

  if (ajaxData.is_generating === true) {
    contentCards = (
      <GeneratingState text="周广告投放策略正在制定中，请稍候 ~" />
    );
  } else if (ajaxData.success === false) {
    contentCards = (
      <ReportFailureState
        rerunParams={{
          asins: ajaxData.asins || [],
          country: country,
          current_time: ajaxData.current_time || '',
          es_id: ajaxData.es_id || '',
          job_id: ajaxData.job_id || target_job_id || job_id || '',
          parent_asin: parent_asin,
          profile_id: profile_id,
          role: ajaxData.role || '',
        }}
        onRerunSuccess={() => {
          // 重新生成成功后，重新获取当前报告数据
          fetchWeekStrategyData();
        }}
        showRerunFeature={isFirstDate}
      />
    );
  } else if (!weekStrategyData) {
    contentCards = (
      <EmptyState
        imageWidth={180}
        text="未查到对应日期的报告"
      />
    );
  } else {
    contentCards = (
      <>
        <AdsTrendCard type="week" />

        <Title level={3} style={{ marginTop: 48 }}>投放策略</Title>
        {/* WeekStrategyData['ads_suggestion'] 的参数在 WeekStrategyData 也有定义，所以这里传入 weekStrategyData */}
        <WeekAnalysisCard ads_suggestion={weekStrategyData} />

        <Title level={3} style={{ marginTop: "48px" }}>本周预期结果</Title>
        <ExpectedResults
          spend={weekStrategyData.week_expected_result.spend}
          sales={weekStrategyData.week_expected_result.sales}
          acos={weekStrategyData.week_expected_result.acos}
          cvr={weekStrategyData.week_expected_result.cvr}
          real_ads_result={_real_ads_result}
        />

        {/* 预算和竞价调整板块 */}
        <Title level={3} style={{ marginTop: "48px" }}>预算和竞价调整</Title>
        <Card data-test-id="week-strategy-budget-bid-adjustment" className="card" style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
          <Flex justify='space-between' style={{ marginBottom: 16 }}>
            <Title level={5} style={{ fontSize: '16px' }}>本周预算</Title>
          </Flex>
          <Row gutter={[24, 24]} style={{ flex: 1 }}>
            <Col span={10}>
              <Statistic
                title={<div style={{ fontSize: '14px', color: '#8c8c8c', marginBottom: 8 }}>建议预算</div>}
                value={weekStrategyData.week_budget.typical}
                prefix={currency}
                precision={2}
                valueStyle={{ color: '#333', fontSize: '24px', fontWeight: 'bold' }}
              />
              <Text
                type={parseFloat(weekStrategyData.week_budget.change_from_last_week) > 0 ? "danger" : "success"}
                style={{ fontSize: '14px', display: 'block', marginTop: 4 }}>
                较上周：{weekStrategyData.week_budget.change_from_last_week}
              </Text>
            </Col>
            <Col span={14}>
              <Statistic
                title={<div style={{ fontSize: '14px', color: '#8c8c8c', marginBottom: 8 }}>动态调整范围</div>}
                value={`${currency}${weekStrategyData.week_budget.min} ~ ${currency}${weekStrategyData.week_budget.max}`}
                valueStyle={{
                  fontSize: '24px',
                  fontWeight: 'bold'
                }}
              />
              <Text style={{ fontSize: '14px', color: '#8c8c8c', display: 'block', marginTop: 4 }}>
                AI 会根据本周广告实际表现动态调整
              </Text>
            </Col>
          </Row>
          <div style={{ marginTop: 'auto', paddingTop: 16 }}>
            <Text style={{ fontSize: '14px', color: '#666', display: 'block' }}>
              {weekStrategyData.week_budget.rationale}
            </Text>
          </div>
        </Card>
        <Card data-test-id="week-strategy-bid-adjustment" className="card" style={{ height: '100%', display: 'flex', flexDirection: 'column', marginTop: "16px" }}>
          <Flex justify='space-between' style={{ marginBottom: 16 }}>
            <Title level={5} style={{ fontSize: '16px' }}>竞价调整</Title>
          </Flex>
          <Row gutter={[24, 24]} style={{ flex: 1 }}>
            <Col span={10}>
              <Statistic
                title={<div style={{ fontSize: '14px', color: '#8c8c8c', marginBottom: 8 }}>建议调整</div>}
                value={weekStrategyData.bid_adjustment_range.min * 100}
                suffix="%"
                precision={0}
                valueStyle={{
                  fontSize: '24px',
                  fontWeight: 'bold',
                  color: '#333'
                }}
              />
            </Col>
            <Col span={14}>
              <Statistic
                title={<div style={{ fontSize: '14px', color: '#8c8c8c', marginBottom: 8 }}>调整范围</div>}
                value={`${(weekStrategyData.bid_adjustment_range.min * 100).toFixed(0)}% ~ ${(weekStrategyData.bid_adjustment_range.max * 100).toFixed(0)}%`}
                valueStyle={{
                  fontSize: '24px',
                  fontWeight: 'bold',
                  color: '#333'
                }}
              />
              <Text style={{ fontSize: '14px', color: '#8c8c8c', display: 'block', marginTop: 4 }}>
                AI 会根据实际情况在调整范围内动态调整
              </Text>
            </Col>
          </Row>
          <div style={{ marginTop: 'auto', paddingTop: 16 }}>
            <Text style={{ fontSize: '14px', color: '#666', display: 'block' }}>
              {weekStrategyData.bid_adjustment_range.rational}
            </Text>
          </div>
        </Card>
        {/* 每日预算与竞价调整板块 */}
        <Flex justify='space-between' align='center' style={{ marginTop: "48px" }}>
          <Title level={3} style={{ margin: 0 }}>每日投放策略</Title>
        </Flex>
        <Card data-test-id="week-strategy-daily-strategy" className="card" style={{ marginTop: "16px" }}>
          <Table
            rowKey={'date'}
            columns={dayBudgetColumns}
            dataSource={dailyStrategyData}
            pagination={false}
            sticky
          />

        </Card>

        {/* 修订记录 */}
        <RevisionHistory
          revision_history={weekStrategyData?.revision_history}
        />
      </>
    );
  }

  const viewCompleteStrategy = () => {
    const title = `周广告投放策略（${ajaxData.start_date || ''}~${ajaxData.end_date || ''}）`;
    const modalData: WeekStrategyModalData = {
      date: ajaxData.target_week,
      current_time: ajaxData.current_time,
      job_id: ajaxData.job_id,
      isCompleteStrategy: true,
    };
    openWeekStrategyModal(modalData, title);
  };

  return (
    <div className='report-modal-div'>
      <div className='report-modal-content'>
        {/* 日期导航器和历史版本选择 */}
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginTop: '24px', marginBottom: '24px', gap: 16 }}>
          <div style={{ flex: 1, overflow: 'hidden' }}>
            <DateNavigator
              initialDate={ajaxData.start_date}
              current_time={ajaxData.current_time}
              job_id={'ads_strategy_week'}
              dateType='range'
              es_id={ajaxData.es_id}
              onDateChange={handleDateChange}
              onVersionChange={handleVersionChange}
            />
          </div>
          {ajaxData.can_edit && showControlBtn && <Button onClick={viewCompleteStrategy}><EditOutlined />修改策略</Button>}
          <FeedbackButton
            feedbackParams={{
              parent_asin: parent_asin,
              profile_id: profile_id,
              job_id: target_job_id || job_id || '',
              es_id: ajaxData.es_id,
              current_time: current_time || '',
            }}
          />
        </div>

        {
          loading ?
            <Loading />
            :
            contentCards
        }
      </div>

      {showReviewArea && ajaxData.can_edit && (
        <ConfirmationSection
          report_status={ajaxData.report_status}
          value={aiFeedbackContent}
          onChange={(e) => setAiFeedbackContent(e.target.value)}
          onApprove={handleApprove}
          onModify={handleModify}
          minRows={6}
          maxRows={8}
          placeholder='请输入修改意见，每行一条。AI将根据您的意见进行修订。
例如：
● 将策略修改为平衡
● 本周主要目标是在排名稳定基础上，控制广告支出成本
● 本周销售目标是8000美元，基于这个目标重新制定投放策略
● 将周五、周六的投放策略更改为激进'
        />
      )}
    </div>
  );
};

export default WeekStrategyContent;
