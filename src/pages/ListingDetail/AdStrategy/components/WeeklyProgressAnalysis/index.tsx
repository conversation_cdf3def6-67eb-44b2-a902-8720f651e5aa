import React from 'react';
import { Card, Col, Row, Typography, Progress, Tooltip, Space } from 'antd';
import styles from './index.less';
import { processNumber, processNumberOrString } from '@/utils/bus';
import { getDiffColor } from '@/utils/common';
import { useModel } from 'umi';

const { Title, Text } = Typography;

// 处理时间显示的函数
const formatDisplayTime = (time: string): string => {
  if (!time) return '';

  try {
    const date = new Date(time);
    const dayOfWeek = date.getDay(); // 0 = 周日, 1 = 周一, ..., 6 = 周六

    if (dayOfWeek === 0) {
      // 如果是周日，显示下一天的零点
      const nextDay = new Date(date);
      nextDay.setDate(date.getDate() + 1);
      nextDay.setHours(0, 0, 0, 0);

      // 格式化显示
      const year = nextDay.getFullYear();
      const month = String(nextDay.getMonth() + 1).padStart(2, '0');
      const day = String(nextDay.getDate()).padStart(2, '0');
      const hours = String(nextDay.getHours()).padStart(2, '0');
      const minutes = String(nextDay.getMinutes()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}`;
    } else {
      // 如果不是周日，显示原数据
      return time;
    }
  } catch (error) {
    // 如果解析失败，返回原数据
    return time;
  }
};

interface WeeklyProgressAnalysisProps {
  data?: Strategy.DayStrategyData['weekly_progress_analysis'];
  time: string;
}

const WeeklyProgressAnalysis: React.FC<WeeklyProgressAnalysisProps> = (props) => {
  const { data, time } = props;
  const { productInfo } = useModel('productInfo');
  if (!data) {
    return null;
  }
  const currency = productInfo?.currency || '$';

  const { budget, sales, acos, cvr, week_performance_vs_target, key_observations } = data;

  const getStatusText = (status: string) => {
    switch (status) {
      case 'on_track':
        return '正常';
      case 'behind':
        return '落后';
      case 'ahead':
        return '超前';
      default:
        return status;
    }
  };

  const getStatusClass = (status: string) => {
    switch (status) {
      case 'on_track':
        return styles.onTrack;
      case 'ahead':
        return styles.ahead;
      case 'behind':
        return styles.behind;
      default:
        return ''; // behind 使用默认红色
    }
  };

  const parsePercentage = (percentStr: string| number): number => {
    if( typeof percentStr === 'number'){
      return percentStr;
    }
    const match = percentStr.match(/(\d+(?:\.\d+)?)/);
    return match ? parseFloat(match[1]) : 0;
  };

  const getProgressColor = (percent: number): string => {
    return percent < 100 ? '#1890ff' : '#ff4d4f';
  };


  // 计算剩余预算
  const calculateRemainingBudget = (): number | string => {
    if (typeof budget.week_budget === 'number' && typeof budget.week_budget_used === 'number') {
      return budget.week_budget - budget.week_budget_used;
    }
    if(typeof budget.week_budget === 'string' && typeof budget.week_budget_used === 'string'){
      return parseFloat(budget.week_budget) - parseFloat(budget.week_budget_used);
    }
    return '-';
  };

  // 计算ACOS差距
  const calculateAcosDiff = (): string => {
    if (typeof acos.current === 'string' && acos.current.toString().includes('%') && typeof acos.target === 'string' && acos.target.toString().includes('%')) {
      return `${processNumber(Number(acos.current.replace('%', '')) - Number(acos.target.replace('%', '')), 2)}%`;
    } else if (typeof acos.current === 'number' && typeof acos.target === 'number') {
      return `${processNumber((acos.current - acos.target)*100, 2)}%`;
    } else if (acos.diff !== null && acos.diff !== undefined && acos.diff !== 'N/A' && acos.diff !== '') {
      return String(processNumberOrString(acos.diff, '%'));
    }

    // 如果接口没有返回差距，自己计算
    const current = parseFloat(String(processNumberOrString(acos.current))) || 0;
    const target = parseFloat(String(processNumberOrString(acos.target))) || 0;
    const diff = current - target;
    const sign = diff >= 0 ? '+' : '';
    return `${sign}${diff.toFixed(2)}%`;
  };

  // 计算CVR差距
  const calculateCvrDiff = (): string => {
    if (typeof cvr.current === 'string' && cvr.current.toString().includes('%') && typeof cvr.target === 'string' && cvr.target.toString().includes('%')) {
      return `${processNumber(Number(cvr.current.replace('%', '')) - Number(cvr.target.replace('%', '')), 2)}%`;
    } else if (typeof cvr.current === 'number' && typeof cvr.target === 'number') {
      return `${processNumber((cvr.current - cvr.target)*100, 2)}%`;
    }
    if (cvr.diff !== null && cvr.diff !== undefined && cvr.diff !== 'N/A' && cvr.diff !== '') {
      return String(processNumberOrString(cvr.diff, '%'));
    }

    // 如果接口没有返回差距，自己计算
    const current = parseFloat(String(processNumberOrString(cvr.current))) || 0;
    const target = parseFloat(String(processNumberOrString(cvr.target))) || 0;
    const diff = current - target;
    const sign = diff >= 0 ? '+' : '';
    return `${sign}${diff.toFixed(2)}%`;
  };

  // 计算销售比例 （当前值 / 目标值）* 100
  const calculateSalesProgress = (): number => {
    if (typeof sales.current === 'number' && typeof sales.target === 'number') {
      return (sales.current / sales.target) * 100;
    }
    if(typeof sales.current === 'string' && typeof sales.target === 'string'){
      return (parseFloat(sales.current) / parseFloat(sales.target)) * 100;
    }
    return 0;
  };

  // 计算花费比例 （当前花费 / 周预算）* 100
  const calculateSpendProgress = (): number => {
    if (typeof budget.week_budget_used === 'number' && typeof budget.week_budget === 'number') {
      return (budget.week_budget_used / budget.week_budget) * 100;
    }
    if(typeof budget.week_budget_used === 'string' && typeof budget.week_budget === 'string'){
      return (parseFloat(budget.week_budget_used) / parseFloat(budget.week_budget)) * 100;
    }
    return 0;
  };


  return (
    <div>
      <Space style={{ marginTop: 48 }}>
        <Title level={3}>周目标进度评估</Title>
        {time && <Text type="secondary">数据截止至 {formatDisplayTime(time)} (站点时间)</Text>}
      </Space>

      <Row gutter={[16, 16]} className='card-row'>
        <Col span={6}>
          <Card data-test-id="weekly-progress-analysis-current-spend" className={`${styles.metricCard} card`}>
            <div className={styles.metricHeader}>
              <Title level={5} className={styles.metricTitle}>当前花费</Title>
            </div>
            <div className={styles.metricValue}>{`${currency}${budget.week_budget_used}`}</div>
            <div className={styles.metricDetails}>
              <div className={styles.detailItem}>
                <Tooltip title={calculateSpendProgress().toFixed(2)+ '%'}>
                  <Progress
                    percent={parsePercentage(calculateSpendProgress())}
                    size="small"
                    showInfo={false}
                    // format={() => calculateSpendProgress().toFixed(2)+ '%'}
                    strokeColor={getProgressColor(parsePercentage(calculateSpendProgress()))}
                  />
                </Tooltip>
              </div>
              <div className={styles.detailItem}>
                <Text type="secondary">周预算</Text>
                <Text style={{ fontWeight: '500', fontSize: 16 }}>{`${currency}${budget.week_budget}`}</Text>
              </div>
              <div className={styles.detailItem}>
                <Text type="secondary">剩余</Text>
                <Text style={{ fontWeight: '500', fontSize: 16 }}>{`${currency}${calculateRemainingBudget()}`}</Text>
              </div>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card data-test-id="weekly-progress-analysis-current-sales" className={`${styles.metricCard} card`}>
            <div className={styles.metricHeader}>
              <Title level={5} className={styles.metricTitle}>当前销售</Title>
            </div>
            <div className={styles.metricValue}>{`${currency}${sales.current}`}</div>
            <div className={styles.metricDetails}>
              <div className={styles.detailItem}>
                <Tooltip title={calculateSalesProgress().toFixed(2)+ '%'}>
                  <Progress
                    percent={parsePercentage(calculateSalesProgress())}
                    size="small"
                    showInfo={false}
                    // format={() => calculateSalesProgress().toFixed(2)+ '%'}
                    strokeColor={getProgressColor(calculateSalesProgress())}
                  />
                </Tooltip>
              </div>
              <div className={styles.detailItem}>
                <Text type="secondary">目标销售</Text>
                <Text style={{ fontWeight: '500', fontSize: 16 }}>{`${currency}${sales.target}`}</Text>
              </div>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card data-test-id="weekly-progress-analysis-current-acos" className={`${styles.metricCard} card`}>
            <div className={styles.metricHeader}>
              <Title level={5} className={styles.metricTitle}>当前ACOS</Title>
            </div>
            <div className={styles.metricValue}>{processNumberOrString(acos.current, '%')}</div>
            <div className={styles.metricDetails}>
              <div className={styles.detailItem}>
                <Text type="secondary">目标ACOS</Text>
                <Text style={{ fontWeight: '500', fontSize: 16 }}>{processNumberOrString(acos.target, '%')}</Text>
              </div>
              <div className={styles.detailItem}>
                <Text type="secondary">差距</Text>
                <Text style={{ fontWeight: '500', fontSize: 16, color: getDiffColor(calculateAcosDiff()) }}>{calculateAcosDiff()}</Text>
              </div>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card data-test-id="weekly-progress-analysis-current-cvr" className={`${styles.metricCard} card`}>
            <div className={styles.metricHeader}>
              <Title level={5} className={styles.metricTitle}>当前CVR</Title>
            </div>
            <div className={styles.metricValue}>{processNumberOrString(cvr.current, '%')}</div>
            <div className={styles.metricDetails}>
              <div className={styles.detailItem}>
                <Text type="secondary">目标CVR</Text>
                <Text style={{ fontWeight: '500', fontSize: 16 }}>{processNumberOrString(cvr.target, '%')}</Text>
              </div>
              <div className={styles.detailItem}>
                <Text type="secondary">差距</Text>
                <Text style={{ fontWeight: '500', fontSize: 16, color: getDiffColor(calculateCvrDiff()) }}>{calculateCvrDiff()}</Text>
              </div>
            </div>
          </Card>
        </Col>

        <Col span={24}>
          <Card data-test-id="weekly-progress-analysis-overall-progress" className="card">
            <div className={styles.mainStrategy}>
              <div className={styles.strategyContent}>
                <Title level={5}>总体进度</Title>
                <Title level={3} className={`${styles.strategyValue} ${getStatusClass(week_performance_vs_target)}`}>
                  {getStatusText(week_performance_vs_target)}
                </Title>
              </div>
              <ul className="goalList" style={{ overflowY: 'auto', maxHeight: '180px' }}>
                {key_observations.map((desc: string, index: number) => (
                  <li key={index} className="goalItem">
                    {desc && <div className="goalDot"></div>}
                    <Text style={{ textAlign: 'left', lineHeight: '1.5' }}>{desc}</Text>
                  </li>
                ))}
              </ul>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default WeeklyProgressAnalysis;