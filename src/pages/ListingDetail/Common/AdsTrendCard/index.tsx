import React, { useEffect, useState, useMemo } from 'react';
import { Card, Flex, Typography, Button, Modal, Checkbox, Form, Spin, Row, Col, Tooltip, Divider } from 'antd';
import dayjs, { type Dayjs } from 'dayjs';
import { SettingOutlined, QuestionCircleOutlined, PlusOutlined, CloseOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import { getAdsTrend } from '@/services/ibidder_api/operation';
import AdsTrendChart from '../../AdStrategy/components/AdsTrendChartTab1';
import { getBudgetColor, formatCurrency } from '@/utils/bus';
import { countryTimezoneMap } from '@/utils/bus';
import { TOOLTIP_DESCRIPTIONS } from '../../AdStrategy/components/CampaignDetailModal/utils/tooltipConfig';
import TrendRangePicker from './components/TrendRangePicker';
import styles from './style.less';
import { chartColors } from '@/utils/common';

const { Text, Title } = Typography;

type VisibleState = {
  spend: boolean;
  sales: boolean;
  acos: boolean;
  cvr: boolean;
  orders: boolean;
  clicks: boolean;
  impressions: boolean;
  ctr: boolean;
  cpc: boolean;
  sku_sales: boolean;
  cpa: boolean;
};

type MetricKey = keyof VisibleState;

interface BaseMetric {
  id: MetricKey;
  title: string;
  desc: string;
}

// 处理百分比字符串转数字的工具函数
export const toNumber = (value: string | number): number => {
  if (typeof value === 'number') return value;
  if (typeof value === 'string') {
    // 去掉%符号并转换为数字
    const numStr = value.replace('%', '');
    const num = parseFloat(numStr);
    return isNaN(num) ? 0 : num;
  }
  return 0;
};

// 基础指标配置
const BASE_METRICS: BaseMetric[] = [
  { id: 'spend', title: '广告花费', desc: TOOLTIP_DESCRIPTIONS.spend },
  { id: 'sales', title: '广告销售额', desc: TOOLTIP_DESCRIPTIONS.sales },
  { id: 'acos', title: 'ACOS', desc: TOOLTIP_DESCRIPTIONS.acos },
  { id: 'cvr', title: 'CVR', desc: TOOLTIP_DESCRIPTIONS.cvr },
  { id: 'orders', title: '订单量', desc: TOOLTIP_DESCRIPTIONS.orders },
  { id: 'clicks', title: '点击量', desc: TOOLTIP_DESCRIPTIONS.clicks },
  { id: 'impressions', title: '曝光量', desc: TOOLTIP_DESCRIPTIONS.impressions },
  { id: 'ctr', title: 'CTR', desc: TOOLTIP_DESCRIPTIONS.ctr },
  { id: 'cpc', title: 'CPC', desc: TOOLTIP_DESCRIPTIONS.cpc },
  { id: 'sku_sales', title: '直接成交销售额', desc: TOOLTIP_DESCRIPTIONS.sku_sales },
  { id: 'cpa', title: 'CPA', desc: TOOLTIP_DESCRIPTIONS.cpa },
];

// 默认选中的指标
const DEFAULT_SELECTED_METRICS: MetricKey[] = ['spend', 'sales', 'acos', 'cvr', 'orders', 'clicks'];

// 图表颜色配置
const CHART_COLORS = chartColors

// 本地存储键名
const CUSTOM_METRICS_STORAGE_KEY = 'ads_trend_custom_metrics';

interface AdsTrendCardProps {
  type: 'month' | 'week' | 'day';
  style?: React.CSSProperties;
}

// 计算预设时间范围的函数
const computePresetRange = (
  type: 'month' | 'week' | 'day',
  count: number,
  country: string
): [dayjs.Dayjs, dayjs.Dayjs] => {
  const timezone = countryTimezoneMap[country as keyof typeof countryTimezoneMap] || 'UTC';
  const now = dayjs().tz(timezone);

  if (type === 'month') {
    const start = now.subtract(count - 1, 'month').startOf('month');
    const end = now.endOf('month');
    return [start, end];
  } else if (type === 'week') {
    const start = now.subtract(count - 1, 'week').startOf('week');
    const end = now.endOf('week');
    return [start, end];
  } else {
    const start = now.subtract(count - 1, 'day').startOf('day');
    const end = now.endOf('day');
    return [start, end];
  }
};

// 存储工具函数
const setStoredMetrics = (metrics: MetricKey[]) => {
  try {
    localStorage.setItem(CUSTOM_METRICS_STORAGE_KEY, JSON.stringify(metrics));
  } catch (error) {
    console.warn('Failed to store metrics:', error);
  }
};

// 本地存储工具函数
const getStoredMetrics = (): MetricKey[] => {
  try {
    const stored = localStorage.getItem(CUSTOM_METRICS_STORAGE_KEY);
    if (stored) {
      const parsed = JSON.parse(stored);
      if (Array.isArray(parsed)) {
        const validMetricIds = BASE_METRICS.map(metric => metric.id);
        const filteredMetrics = parsed.filter((metricId: string) =>
          validMetricIds.includes(metricId as MetricKey)
        );
        if (filteredMetrics.length >= 1 && filteredMetrics.length <= 6) {
          return filteredMetrics;
        }
        if (filteredMetrics.length > 0) {
          return filteredMetrics.slice(0, 6);
        }
      }
    }
  } catch (error) {
    console.warn('Failed to parse stored metrics:', error);
  }
  return DEFAULT_SELECTED_METRICS;
};

const AdsTrendCard: React.FC<AdsTrendCardProps> = React.memo((props) => {
  const { type } = props;
  const { productInfo } = useModel('productInfo');
  const currency = productInfo?.currency || '$';
  const asins = productInfo?.asins || [];
  const parent_asin = productInfo?.parent_asin || '';
  const profile_id = productInfo?.profile_id as number | undefined;

  const [range, setRange] = useState<[Dayjs, Dayjs]>(() => {
    if (type === 'day') return computePresetRange('day', 14, 'US');
    if (type === 'week') return computePresetRange('week', 14, 'US');
    return computePresetRange('month', 6, 'US');
  });
  const [rangeForChart, setRangeForChart] = useState(range);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<API.AdsTrendResponse | null>(null);

  const [visible, setVisible] = useState<VisibleState>({
    spend: true,
    sales: true,
    acos: true,
    cvr: true,
    orders: true,
    clicks: true,
    impressions: false,
    ctr: false,
    cpc: false,
    sku_sales: false,
    cpa: false,
  });

  // 自定义指标相关状态
  const [selectedMetrics, setSelectedMetrics] = useState<MetricKey[]>(() => getStoredMetrics());
  const [isCustomModalVisible, setIsCustomModalVisible] = useState(false);
  const [form] = Form.useForm();

  const fetchData = async (start: Dayjs, end: Dayjs) => {
    if (!profile_id || !parent_asin) return;
    setLoading(true);
    try {
      // For week and month types, adjust start/end times to period boundaries
      let apiStartTime: string;
      let apiEndTime: string;

      if (type === 'week') {
        // For week: start should be Monday of the start week, end should be Sunday of the end week
        const startWeekBegin = start.startOf('week'); // Monday
        const endWeekEnd = end.endOf('week'); // Sunday
        apiStartTime = startWeekBegin.format('YYYY-MM-DD');
        apiEndTime = endWeekEnd.format('YYYY-MM-DD');
      } else if (type === 'month') {
        // For month: start should be 1st day of start month, end should be last day of end month
        const startMonthBegin = start.startOf('month');
        const endMonthEnd = end.endOf('month');
        apiStartTime = startMonthBegin.format('YYYY-MM-DD');
        apiEndTime = endMonthEnd.format('YYYY-MM-DD');
      } else {
        // For day type, use original logic
        apiStartTime = start.format('YYYY-MM-DD');
        apiEndTime = end.format('YYYY-MM-DD');
      }

      const res = await getAdsTrend({
        asins,
        parent_asin,
        profile_id: Number(profile_id),
        start_time: apiStartTime,
        end_time: apiEndTime,
        type,
      });
      // @ts-ignore
      if (res && (res.code === 200 || res.list || res.sum)) {
        // Some request util may return data directly
        const payload: API.AdsTrendResponse = (res.data || res) as any;
        setData(payload);
        setRangeForChart([start, end]);
      } else {
        setData(null);
        setRangeForChart([start, end]);
      }
    } catch (e) {
      setData(null);
      setRangeForChart([start, end]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (range) {
      fetchData(range[0], range[1]);
    }
  }, [type, range?.[0]?.valueOf(), range?.[1]?.valueOf(), profile_id, parent_asin]);

  const chartData = useMemo(() => {
    if (
      range?.[0]?.valueOf() !== rangeForChart?.[0]?.valueOf() ||
      range?.[1]?.valueOf() !== rangeForChart?.[1]?.valueOf()
    ) {
      return {
        xAxisData: [],
        spend: [],
        sales: [],
        acos: [],
        cvr: [],
        orders: [],
        clicks: [],
        impressions: [],
        ctr: [],
        cpc: [],
        spend_ratio: [],
        sku_sales: [],
        cpa: [],
        sales_ratio: [],
      };
    }

    const xAxisData: string[] = [];
    const spend: number[] = [];
    const sales: number[] = [];
    const acos: number[] = [];
    const cvr: number[] = [];
    const orders: number[] = [];
    const clicks: number[] = [];
    const impressions: number[] = [];
    const ctr: number[] = [];
    const cpc: number[] = [];
    const sku_sales: number[] = [];
    const cpa: number[] = [];

    const expected_spend: number[] = [];
    const target_sales: number[] = [];
    const target_acos: number[] = [];
    const target_cvr: number[] = [];

    if (data?.list?.length) {
      data.list.forEach((item) => {
        const startDate = item.startDate;
        if (type === 'week') {
          const startD = dayjs(startDate);
          const endD = startD.add(6, 'day');
          xAxisData.push(`${startD.format('YYYY-MM-DD')}～${endD.format('YYYY-MM-DD')}`);
        } else if (type === 'month') {
          xAxisData.push(dayjs(startDate).format('YYYY-MM'));
        } else {
          xAxisData.push(startDate);
        }
        const spendValue = Number(item.spend || 0);
        const salesValue = Number(item.sales || 0);
        const ordersValue = Number(item.orders || 0);
        const clicksValue = Number(item.clicks || 0);
        const impressionsValue = Number(item.impressions || 0);
        const cpcValue = Number(item.cpc || 0);
        const cpaValue = Number(item.cpa || 0);

        spend.push(spendValue);
        sales.push(salesValue);
        acos.push(toNumber(item.acos));
        cvr.push(toNumber(item.cvr));
        orders.push(ordersValue);
        clicks.push(clicksValue);
        impressions.push(impressionsValue);
        ctr.push(toNumber(item.ctr));
        cpc.push(cpcValue);
        // 直接成交销售额 - 使用API返回的sku_sales字段
        sku_sales.push(Number(item.sku_sales || 0));
        cpa.push(cpaValue);

        const expectedResults = item.strategy_expected_data?.expected_results;
        if (expectedResults) {
          expected_spend.push(Number(expectedResults.spend));
          target_sales.push(Number(expectedResults.sales));
          target_acos.push(toNumber(expectedResults.acos));
          target_cvr.push(toNumber(expectedResults.cvr));
        } else {
          expected_spend.push(NaN);
          target_sales.push(NaN);
          target_acos.push(NaN);
          target_cvr.push(NaN);
        }
      });
    }

    return {
      xAxisData,
      spend: visible.spend ? spend : [],
      sales: visible.sales ? sales : [],
      acos: visible.acos ? acos : [],
      cvr: visible.cvr ? cvr : [],
      orders: visible.orders ? orders : [],
      clicks: visible.clicks ? clicks : [],
      impressions: visible.impressions ? impressions : [],
      ctr: visible.ctr ? ctr : [],
      cpc: visible.cpc ? cpc : [],
      sku_sales: visible.sku_sales ? sku_sales : [],
      cpa: visible.cpa ? cpa : [],
      expected_spend,
      target_sales,
      target_acos,
      target_cvr,
    };
  }, [data, type, visible, range, rangeForChart]);

  const keyForChart = useMemo(() => {
    // Force re-mount to update options due to AdsTrendChart's internal effect deps
    return `trend-${type}-${rangeForChart?.[0]?.valueOf()}-${rangeForChart?.[1]?.valueOf()}-${JSON.stringify(
      visible,
    )}`;
  }, [type, rangeForChart, visible]);

  const sum = data?.sum;
  const prev = data?.previous_sum as any;

  // 生成指标数据的函数
  const createMetricData = (
    baseMetric: { id: keyof VisibleState; title: string; desc: string },
    colorIndex: number,
  ) => {
    const { id, title, desc } = baseMetric;
    const color = CHART_COLORS[colorIndex % CHART_COLORS.length];

    let value: string;
    let prevValue: string;
    let ratio: string | undefined;

    switch (id) {
      case 'spend':
        value = formatCurrency(sum?.spend || 0, currency);
        prevValue = formatCurrency(prev?.spend || 0, currency);
        ratio = prev?.spend_ratio;
        break;
      case 'sales':
        value = formatCurrency(sum?.sales || 0, currency);
        prevValue = formatCurrency(prev?.sales || 0, currency);
        ratio = prev?.sales_ratio;
        break;
      case 'acos':
        value = sum?.acos || '-';
        prevValue = prev?.acos || '-';
        ratio = prev?.acos_ratio;
        break;
      case 'cvr':
        value = sum?.cvr || '-';
        prevValue = prev?.cvr || '-';
        ratio = prev?.cvr_ratio;
        break;
      case 'orders':
        value = String(sum?.orders || 0);
        prevValue = String(prev?.orders || 0);
        ratio = prev?.orders_ratio;
        break;
      case 'clicks':
        value = String(sum?.clicks || 0);
        prevValue = String(prev?.clicks || 0);
        ratio = prev?.clicks_ratio;
        break;
      case 'impressions':
        value = String(sum?.impressions || 0);
        prevValue = String(prev?.impressions || 0);
        ratio = prev?.impressions_ratio;
        break;
      case 'ctr':
        value = sum?.ctr || '-';
        prevValue = prev?.ctr || '-';
        ratio = prev?.ctr_ratio;
        break;
      case 'cpc':
        value = formatCurrency(sum?.cpc || 0, currency);
        prevValue = formatCurrency(prev?.cpc || 0, currency);
        ratio = prev?.cpc_ratio;
        break;
      case 'sku_sales':
        value = formatCurrency(sum?.sku_sales || 0, currency);
        prevValue = formatCurrency(prev?.sku_sales || 0, currency);
        ratio = prev?.sku_sales_ratio;
        break;
      case 'cpa':
        value = formatCurrency(sum?.cpa || 0, currency);
        prevValue = formatCurrency(prev?.cpa || 0, currency);
        ratio = prev?.cpa_ratio;
        break;
      default:
        value = '-';
        prevValue = '-';
        ratio = undefined;
    }

    return {
      id,
      title,
      desc,
      color,
      value,
      prevValue,
      ratio,
      onClick: () => setVisible((v) => ({ ...v, [id]: !v[id] })),
    };
  };

  // 根据选中的指标生成 metrics 数组
  const metrics = selectedMetrics
    .map((metricId, index) => {
      const baseMetric = BASE_METRICS.find((m) => m.id === metricId);
      if (!baseMetric) return null;
      return createMetricData(baseMetric, index);
    })
    .filter(Boolean) as ReturnType<typeof createMetricData>[];

  // 删除指标的函数
  const handleRemoveMetric = (metricId: MetricKey) => {
    const newSelectedMetrics = selectedMetrics.filter(id => id !== metricId);
    if (newSelectedMetrics.length >= 1) { // 至少保留一个指标
      setSelectedMetrics(newSelectedMetrics);
      setStoredMetrics(newSelectedMetrics);
    }
  };

  const MetricCard = ({
    id,
    title,
    desc,
    color,
    value,
    prevValue,
    ratio,
    onClick,
  }: ReturnType<typeof createMetricData>) => {
    const [isHovered, setIsHovered] = useState(false);

    const handleCloseClick = (e: React.MouseEvent) => {
      e.stopPropagation(); // 阻止事件冒泡，避免触发卡片的onClick
      handleRemoveMetric(id);
    };

    return (
      <Col xs={12} sm={12} md={6} lg={4} xl={4}>
        <div
          onClick={onClick}
          className={styles.metricCard}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          style={{
            background: visible[id] ? '#fff' : '#F9FAFB',
            borderRadius: '4px',
            boxShadow: '0 2px 8px rgba(0,0,0,0.09)',
            padding: '16px 20px',
            display: 'flex',
            alignItems: 'center',
            cursor: 'pointer',
            gap: '16px',
            borderLeft: `4px solid ${visible[id] ? color : '#F9FAFB'}`,
            position: 'relative',
          }}
        >
          {/* 关闭按钮 */}
          {isHovered && (
            <div
              onClick={handleCloseClick}
              style={{
                position: 'absolute',
                top: '8px',
                right: '8px',
                width: '20px',
                height: '20px',
                borderRadius: '50%',
                backgroundColor: 'rgba(0, 0, 0, 0.6)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                cursor: 'pointer',
                zIndex: 10,
              }}
            >
              <CloseOutlined style={{ color: '#fff', fontSize: '12px' }} />
            </div>
          )}

          <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
            <Text style={{ color: '#595959', fontSize: '14px' }}>
              {title}
              <Tooltip title={desc} overlayInnerStyle={{ whiteSpace: 'pre-wrap', width: 280 }}>
                <QuestionCircleOutlined
                  style={{ color: '#86909C', fontSize: '14px', marginLeft: '4px' }}
                />
              </Tooltip>
            </Text>
            <div style={{ fontWeight: 'bold', fontSize: '24px', lineHeight: '1.2' }}>{value}</div>
            <Tooltip
              placement="bottomRight"
              overlayInnerStyle={{ width: 400 }}
              title={
                <div
                  style={{
                    whiteSpace: 'nowrap',
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 10,
                    padding: 8,
                    width: 700,
                  }}
                >
                  <div>{title}</div>
                  <div style={{ display: 'flex', gap: 20 }}>
                    <span>本期：{value}</span>
                    <span>
                      {data?.sum?.start_time}~{data?.sum?.end_time}
                    </span>
                  </div>
                  <div style={{ display: 'flex', gap: 20 }}>
                    <span>环比：{prevValue}</span>
                    <span>
                      {data?.previous_sum?.start_time}~{data?.previous_sum?.end_time}
                    </span>
                    <span style={{ color: getBudgetColor(0, ratio || 0) }}>
                      {ratio ? (ratio.startsWith('-') ? ratio : '+' + ratio) : '-'}
                    </span>
                  </div>
                </div>
              }
            >
              <div
                style={{
                  color: '#8c8c8c',
                  fontSize: '12px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                }}
              >
                <span>{prevValue}</span>
                <Divider type="vertical" />
                <span style={{ color: getBudgetColor(0, ratio || 0) }}>
                  {ratio ? (ratio.startsWith('-') ? ratio : '+' + ratio) : '-'}
                </span>
              </div>
            </Tooltip>
          </div>
        </div>
      </Col>
    );
  };

  // 自定义指标处理函数
  const handleCustomMetricsClick = () => {
    form.setFieldsValue({
      selectedMetrics: selectedMetrics
    });
    setIsCustomModalVisible(true);
  };

  // 添加指标卡片组件
  const AddMetricCard = () => (
    <Col xs={12} sm={12} md={6} lg={4} xl={4}>
      <div
        onClick={handleCustomMetricsClick}
        className={styles.metricCard}
        style={{
          borderRadius: '4px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.09)',
          padding: '16px 20px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          cursor: 'pointer',
          gap: '8px',
          width: '100%',
          height: '100%',
        }}
      >
        <PlusOutlined style={{ fontSize: '16px' }} />
        <Text>添加指标</Text>
      </div>
    </Col>
  );

  const onRangeChange = (dates: null | (Dayjs | null)[]) => {
    if (!dates || dates.length !== 2) return;
    setRange([dates[0]!, dates[1]!]);
  };

  const handleCustomMetricsConfirm = () => {
    form.validateFields().then((values) => {
      const newSelectedMetrics = values.selectedMetrics || [];

      if (newSelectedMetrics.length < 1 || newSelectedMetrics.length > 6) {
        return;
      }

      setSelectedMetrics(newSelectedMetrics);
      setStoredMetrics(newSelectedMetrics);

      // 更新 visible 状态
      const newVisible: VisibleState = {
        spend: newSelectedMetrics.includes('spend'),
        sales: newSelectedMetrics.includes('sales'),
        acos: newSelectedMetrics.includes('acos'),
        cvr: newSelectedMetrics.includes('cvr'),
        orders: newSelectedMetrics.includes('orders'),
        clicks: newSelectedMetrics.includes('clicks'),
        impressions: newSelectedMetrics.includes('impressions'),
        ctr: newSelectedMetrics.includes('ctr'),
        cpc: newSelectedMetrics.includes('cpc'),
        sku_sales: newSelectedMetrics.includes('sku_sales'),
        cpa: newSelectedMetrics.includes('cpa'),
      };
      setVisible(newVisible);
      setIsCustomModalVisible(false);
    }).catch((error) => {
      console.error('Form validation failed:', error);
    });
  };

  const handleCustomMetricsCancel = () => {
    form.resetFields();
    setIsCustomModalVisible(false);
  };

  // 初始化 visible 状态基于选中的指标
  useEffect(() => {
    const newVisible: VisibleState = {
      spend: selectedMetrics.includes('spend'),
      sales: selectedMetrics.includes('sales'),
      acos: selectedMetrics.includes('acos'),
      cvr: selectedMetrics.includes('cvr'),
      orders: selectedMetrics.includes('orders'),
      clicks: selectedMetrics.includes('clicks'),
      impressions: selectedMetrics.includes('impressions'),
      ctr: selectedMetrics.includes('ctr'),
      cpc: selectedMetrics.includes('cpc'),
      sku_sales: selectedMetrics.includes('sku_sales'),
      cpa: selectedMetrics.includes('cpa'),
    };
    setVisible(newVisible);
  }, [selectedMetrics]);

  // 是否显示添加指标卡片
  const showAddMetricCard = selectedMetrics.length < 6;

  return (
    <Card data-testid="ads-trend-card" className="card" style={{ ...props.style }}>
      <Flex justify="space-between" align="center" style={{ marginBottom: 24 }}>
        <Title level={4}>广告走势</Title>

        <Flex gap={8}>
          <TrendRangePicker type={type} value={range} onChange={onRangeChange} />
          <Button icon={<SettingOutlined />} onClick={handleCustomMetricsClick}>
            自定义指标
          </Button>
        </Flex>
      </Flex>

      <Spin spinning={loading}>
        <Row gutter={[16, 16]}>
          {metrics.map((metric) => (
            <MetricCard {...metric} key={metric.id} />
          ))}
          {showAddMetricCard && <AddMetricCard />}
        </Row>
        <div style={{ width: '100%', height: 300 }}>
          <AdsTrendChart
            key={keyForChart}
            currency={currency}
            data={chartData}
            hideLegend
            dateType={type}
            chartOption={{
              axisLabel: type === 'week' ? { interval: 0 } : {},
            }}
            metricsOrder={selectedMetrics}
          />
        </div>
      </Spin>

      {/* 自定义指标选择弹框 */}
      <Modal
        title={<>自定义指标 <Text type='secondary' style={{ fontWeight: 'semibold' }}>最少选择1项，最多选择6项</Text></>}
        open={isCustomModalVisible}
        onOk={handleCustomMetricsConfirm}
        onCancel={handleCustomMetricsCancel}
        okText="保存并应用"
        cancelText="取消"
        width={700}
      >
        <Form
          form={form}
          layout="vertical"
          style={{ marginTop: 24 }}
        >
          <Form.Item
            name="selectedMetrics"
            rules={[
              {
                validator: (_, value) => {
                  if (!value || value.length < 1) {
                    return Promise.reject(new Error('请至少选择1个指标'));
                  }
                  if (value.length > 6) {
                    return Promise.reject(new Error('最多只能选择6个指标'));
                  }
                  return Promise.resolve();
                },
              },
            ]}
          >
            <Checkbox.Group style={{ width: '100%' }}>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(4, 1fr)', gap: '16px 24px' }}>
                {BASE_METRICS.map((metric) => (
                  <Checkbox
                    key={metric.id}
                    value={metric.id}
                    style={{
                      display: 'flex',
                      alignItems: 'flex-start',
                      whiteSpace: 'normal',
                      lineHeight: '1.4',
                    }}
                  >
                    <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                      <span>{metric.title}</span>
                    </div>
                  </Checkbox>
                ))}
              </div>
            </Checkbox.Group>
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  );
});

export default AdsTrendCard;
