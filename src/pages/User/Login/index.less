@import '@/utils/common.less';

.action {
  margin-left: 8px;
  color: rgba(0, 0, 0, 0.2);
  font-size: 24px;
  vertical-align: middle;
  cursor: pointer;
  transition: color 0.3s;

  &:hover {
    color: #1890ff;
  }
}

.lang {
  width: 42px;
  height: 42px;
  line-height: 42px;
  position: fixed;
  top: 24px;
  right: 24px;
  border-radius: 6px;
  text-align: center;

  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }
}

.container {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

.leftPanel {
  flex: 1;
  background: @primaryColorAlpha;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 60px 40px;
  position: relative;
}

.rightPanel {
  width: 50%;
  margin: 0 auto;
  background: #f6f6f6;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 40px 50px;
  box-sizing: border-box;
  position: relative;
}

.dashboardPreview {
  width: 100%;
  max-width: 400px;
  margin-bottom: 40px;
}

.previewImage {
  width: 100%;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.leftContent {
  text-align: center;
  color: #fff;
}

.leftTitle {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 16px;
  line-height: 1.2;
}

.leftSubtitle {
  font-size: 16px;
  opacity: 0.9;
  line-height: 1.5;
}

.loginFormContainer {
  width: 480px;
  margin: 0 auto;
  background: #fff;
  border-radius: 12px;
  padding: 80px 60px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loginHeader {
  text-align: center;
  margin-bottom: 32px;
}

.loginLogo {
  height: 36px;
  margin-bottom: 20px;
}

.loginTitle {
  font-size: 28px;
  color: #333;
  font-weight: 500;
  text-align: left;
}

.loginTypeSwitcher {
  text-align: left;
  margin-bottom: 40px;
  padding-left: 0;

  a {
    font-size: 18px;
    color: #8c8c8c;
    text-decoration: none;
    margin-right: 24px;
    transition: color 0.3s, border-bottom-color 0.3s;
    padding-bottom: 8px;

    &:hover {
      color: #1890ff;
    }

    &:last-child {
      margin-right: 0;
    }
  }
}

.activeLoginType {
  font-weight: 500;
  color: @primaryColor !important;
  position: relative;
  border-bottom: 2px solid @primaryColor;
}

.inactiveLoginType {
  color: #8c8c8c;
  border-bottom: 2px solid transparent;
}

.wechatLoginContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  min-height: 370px;
  padding-top: 20px;
}

.wechatQrCode {
  width: 160px;
  height: 160px;
  margin-top: 20px;
  margin-bottom: 20px;
  border: 1px solid #e8e8e8;
  padding: 8px;
  background-color: #fff;
}

.wechatScanPrompt {
  font-size: 14px;
  color: #595959;
  margin-bottom: 20px;
}

.accountForm {
  .ant-form-item {
    margin-bottom: 20px !important;
  }
}

.prefixIcon {
  color: #bfbfbf;
  margin-right: 8px;
  font-size: 16px;
}

.accountLinks {
  display: flex;
  justify-content: space-between;
  margin-top: -10px;
  margin-bottom: 32px;
  font-size: 14px;
}

.loginFormButton {
  width: 100%;
  height: 44px !important;
  font-size: 16px !important;
}

.registerLinkContainer {
  text-align: center;
  margin-top: 54px;
  font-size: 14px;
  color: #8c8c8c;

  a {
    margin-left: 4px;
  }
}

.registerLinkWechat {
  margin-top: auto;
  padding-bottom: 10px;
  width: 100%;
}
.container {
  :global {
    .ant-input-affix-wrapper {
      padding-top: 10px !important;
      padding-bottom: 10px !important;
  
      .ant-input {
        font-size: 14px;
      }
    }
  
    .ant-form-item-label>label {}
  
    .ant-form-item-explain-error {
      font-size: 12px;
      padding-top: 2px;
    }
  }
}


.helpFloat {
  position: fixed;
  bottom: 24px;
  right: 24px;
  width: 42px;
  height: 42px;
  background: #FFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  z-index: 1000;

  &:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  }

  .anticon {
    color: #fff;
    font-size: 20px;
  }
}

// 忘记密码相关样式
.forgotPasswordContainer {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.forgotPasswordHeader {
  text-align: center;
  margin-bottom: 40px;
  position: relative;
}

.backButton {
  position: absolute;
  left: 0;
  top: 0;
  color: #666;
  padding: 0;

  &:hover {
    color: #1890ff;
  }
}

.forgotPasswordTitle {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 8px;
}

.verificationMethodButtons {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 40px;
  width: 100%;
}

.methodButton {
  height: 50px !important;
  font-size: 16px !important;
  font-weight: 500;
  border-radius: 8px;
}

.backToLoginContainer {
  text-align: center;
  margin-top: 20px;
}

.backToLoginLink {
  color: #8c8c8c;
  font-size: 14px;

  &:hover {
    color: #1890ff;
  }
}

.forgotPasswordForm {
  width: 100%;
}

.captchaInput {
  flex: 1;
}

.getCaptchaBtn {
  width: 100px;
  height: 40px;
  font-size: 14px;
  border-radius: 6px;
}

.resetPasswordButton {
  height: 50px !important;
  font-size: 16px !important;
  font-weight: 500;
  border-radius: 8px;
  margin-top: 20px;
}

.confirmButton {
  height: 50px !important;
  font-size: 16px !important;
  font-weight: 500;
  border-radius: 8px;
  margin-top: 20px;
}