import { _performTokenRefreshAttempt, TokenManager } from '@/requestErrorConfig';
import { history } from '@umijs/max';
import { loginPath } from '../../config/routes';

/**
 * 认证工具类
 */
export class AuthUtils {
  /**
   * 检查用户是否已登录
   */
  static isLoggedIn(): boolean {
    return !!TokenManager.getAccessToken();
  }

  /**
   * 用户登出
   */
  static logout(needRedirect: boolean = true): void {
    // 清除所有tokens
    TokenManager.clearAllTokensAndUserInfo();
    // 清除游客模式标记
    localStorage.removeItem('isGuestMode');

    const { pathname } = window.location;

    // 如果当前已经是登录页，直接跳转到登录页（不带任何参数），避免循环
    if (pathname === loginPath) {
      history.push(loginPath);
      return;
    }

    // 如果不在登录页，根据 needRedirect 参数决定是否要附带 redirect url
    if (needRedirect) {
      const redirect = pathname + window.location.search;
      history.push(loginPath + '?redirect=' + encodeURIComponent(redirect));
    } else {
      history.push(loginPath);
    }
  }

  /**
   * 解析JWT token
   */
  static parseJwt(token: string): any {
    try {
      // 解析JWT token的payload部分
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map(function (c) {
            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
          })
          .join('')
      );
      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error('解析JWT token失败:', error);
      return null;
    }
  }

  /**
   * 保存用户信息到localStorage
   */
  static saveUserInfo(userInfo: any): void {
    if (userInfo) {
      localStorage.setItem('user_info', JSON.stringify(userInfo));
    }
  }

  /**
   * 处理登录成功后的token存储
   */
  static handleLoginSuccess(loginData: {
    access_token?: string;
    refresh_token?: string;
    refresh_expires?: number;
    access_expires?: number;
    token?: string;
    user_info?: {
      id: number;
      username: string;
      email: string;
      phone: string;
      user_type: number;
      parent_user_id: number;
      status: number;
      nick_name?: string | null;
      avatar?: string | null;
      create_time: string;
      update_time: string;
    };
  }): void {
    // 存储access_token到localStorage
    if (loginData.access_token) {
      TokenManager.setAccessToken(loginData.access_token);
    }

    // 存储refresh_token
    if (loginData.refresh_token) {
      TokenManager.setRefreshToken(loginData.refresh_token);
    }

    // 存储refresh_token过期时间
    if (loginData.refresh_expires) {
      TokenManager.setRefreshTokenExpires(loginData.refresh_expires);
    }

    // 存储access_token过期时间
    if (loginData.access_expires) {
      TokenManager.setAccessTokenExpires(loginData.access_expires);
    }

    // 兼容旧的token字段
    if (loginData.token) {
      // 如果没有access_token，将token作为access_token使用
      if (!loginData.access_token) {
        TokenManager.setAccessToken(loginData.token);
      }
    }

    // 存储用户信息到localStorage
    if (loginData.user_info) {
      AuthUtils.saveUserInfo(loginData.user_info);
    }

    localStorage.removeItem('isGuestMode');
  }

  /**
   * 获取存储的用户信息
   */
  static getUserInfo(): any {
    const userInfoStr = localStorage.getItem('user_info');
    if (userInfoStr) {
      try {
        return JSON.parse(userInfoStr);
      } catch (error) {
        console.error('解析用户信息失败:', error);
        return null;
      }
    }
    return null;
  }

  /**
   * 获取用户显示名称
   * 优先级：昵称 > 用户名 > 手机号
   */
  static getUserDisplayName(): string {
    const userInfo = AuthUtils.getUserInfo();
    if (userInfo) {
      return userInfo.username || userInfo.nick_name || userInfo.phone || '用户';
    }
    return '用户';
  }

  /**
   * 检查access_token是否即将过期（可选功能）
   */
  static isTokenExpiringSoon(): boolean {
    const token = TokenManager.getAccessToken();
    if (!token) return false;

    try {
      // 解析JWT token的payload
      const payload = JSON.parse(atob(token.split('.')[1]));
      const exp = payload.exp * 1000; // 转换为毫秒
      const now = Date.now();
      const fiveMinutes = 5 * 60 * 1000; // 5分钟

      return exp - now < fiveMinutes;
    } catch (error) {
      console.error('解析token失败:', error);
      return false;
    }
  }

  /**
   * 检查refresh_token是否过期
   */
  static isRefreshTokenExpired(): boolean {
    return TokenManager.isRefreshTokenExpired();
  }

  /**
   * 检查refresh_token是否即将过期
   */
  static isRefreshTokenExpiringSoon(): boolean {
    const expires = TokenManager.getRefreshTokenExpires();
    if (!expires) return true;

    const now = Date.now();
    const oneDay = 24 * 60 * 60 * 1000; // 1天

    return expires - now < oneDay;
  }

  /**
   * 获取refresh_token剩余有效时间（秒）
   */
  static getRefreshTokenRemainingTime(): number {
    const expires = TokenManager.getRefreshTokenExpires();
    if (!expires) return 0;

    const remaining = expires - Date.now();
    return Math.max(0, Math.floor(remaining / 1000));
  }

  /**
   * 检查access_token是否即将过期（基于存储的过期时间）
   */
  static isAccessTokenExpiringSoon(seconds: number = 10): boolean {
    const expiresStr = TokenManager.getAccessTokenExpires();
    if (!expiresStr) return true;

    const expires = parseInt(expiresStr, 10);
    const now = Date.now();
    const thresholdMs = seconds * 1000; // 转换为毫秒

    return expires - now < thresholdMs;
  }

  /**
   * 自动检查并刷新access_token（当剩余时间小于指定秒数时）
   */
  static async autoRefreshAccessTokenIfNeeded(seconds: number = 10): Promise<void> {
    if (AuthUtils.isAccessTokenExpiringSoon(seconds)) {
      await _performTokenRefreshAttempt();
    }
  }
}

export default AuthUtils;
