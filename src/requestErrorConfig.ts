import type { RequestOptions } from '@@/plugin-request/request';
import type { RequestConfig } from '@umijs/max';
import { message, notification } from 'antd';
import { history } from '@umijs/max';
import type { AxiosError } from 'axios';
import AuthUtils from './utils/auth';

// Token 管理相关接口
interface RefreshTokenData {
  access_token: string;
  access_expires?: number;
  refresh_token?: string;
  refresh_expires?: number;
}

interface RefreshApiResponse {
  code: number;
  data: RefreshTokenData;
}

interface ITokenManager {
  getAccessToken: () => string | null;
  getAccessTokenExpires: () => string | null;
  getRefreshToken: () => string | null;
  setAccessToken: (token: string) => void;
  setAccessTokenExpires: (expiresInSeconds: number) => void;
  clearAccessToken: () => void;
  setRefreshToken: (token: string) => void;
  setRefreshTokenExpires: (expiresInSeconds: number) => void;
  getRefreshTokenExpires: () => number | null;
  isRefreshTokenExpired: () => boolean;
  clearAllTokensAndUserInfo: () => void;
}

// Token管理工具
const TokenManager: ITokenManager = {
  // 获取access_token
  getAccessToken: () => {
    return localStorage.getItem('access_token');
  },

  // 获取access_token过期时间
  getAccessTokenExpires: () => {
    return localStorage.getItem('access_token_expires');
  },

  // 获取refresh_token
  getRefreshToken: () => {
    return localStorage.getItem('refresh_token');
  },

  // 设置access_token
  setAccessToken: (token: string) => {
    localStorage.setItem('access_token', token);
  },

  // 设置access_token过期时间
  setAccessTokenExpires: (expiresInSeconds: number) => {
    const expiresAt = Date.now() + (expiresInSeconds * 1000);
    localStorage.setItem('access_token_expires', expiresAt.toString());
  },

  // 清除access_token
  clearAccessToken: () => {
    localStorage.removeItem('access_token');
    localStorage.removeItem('access_token_expires');
  },

  // 设置refresh_token到HttpOnly Cookie
  setRefreshToken: (token: string) => {
    localStorage.setItem('refresh_token', token);
  },

  // 设置refresh_token过期时间
  setRefreshTokenExpires: (expiresInSeconds: number) => {
    const expiresAt = Date.now() + (expiresInSeconds * 1000);
    localStorage.setItem('refresh_token_expires', expiresAt.toString());
  },

  // 获取refresh_token过期时间
  getRefreshTokenExpires: () => {
    const expires = localStorage.getItem('refresh_token_expires');
    return expires ? parseInt(expires, 10) : null;
  },

  // 检查refresh_token是否过期
  isRefreshTokenExpired: () => {
    const expires = TokenManager.getRefreshTokenExpires();
    if (!expires) {
      return true;
    }
    const isExpired = Date.now() > expires;
    return isExpired;
  },

  // 清除所有tokens
  clearAllTokensAndUserInfo: () => {
    localStorage.removeItem('access_token');
    localStorage.removeItem('access_token_expires');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('refresh_token_expires');
    localStorage.removeItem('user_info');
  }
};

/* 辅助方法：尝试刷新token */
const _performTokenRefreshAttempt = async (): Promise<string | null> => {
  try {
    // 调用刷新token的API - 使用原始的fetch方法，避免循环依赖
    const response = await fetch('/api/v1/auth/refresh', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        refresh_token: TokenManager.getRefreshToken(),
      }),
    });
    const data: RefreshApiResponse = await response.json();

    if (data.code === 200) {
      const result = data.data
      if (result?.access_token) {
        TokenManager.setAccessToken(result.access_token);

        if (result.access_expires) {
          TokenManager.setAccessTokenExpires(result.access_expires);
        }
        // 如果返回了新的refresh_token和过期时间，更新它们
        if (result.refresh_token) {
          TokenManager.setRefreshToken(result.refresh_token);
        }
        if (result.refresh_expires) {
          TokenManager.setRefreshTokenExpires(result.refresh_expires);
        }
        return result.access_token;
      }
    }
    AuthUtils.logout();
    return null;
  } catch (error) {
    console.error('刷新token失败:', error);
    AuthUtils.logout();
    return null;
  }
};

/*  刷新token的方法 */
const refreshToken = async (): Promise<string | null> => {
  // 检查refresh_token是否过期
  if (TokenManager.isRefreshTokenExpired()) {
    AuthUtils.logout();
    return null;
  }
  return _performTokenRefreshAttempt();
};

// 错误处理方案： 错误类型
enum ErrorShowType {
  SILENT = 0,
  WARN_MESSAGE = 1,
  ERROR_MESSAGE = 2,
  NOTIFICATION = 3,
  REDIRECT = 9,
}
// 与后端约定的响应数据格式
interface ResponseStructure {
  success: boolean;
  data: any;
  errorCode?: number;
  errorMessage?: string;
  showType?: ErrorShowType;
}

/**
 * @name 错误处理
 * pro 自带的错误处理， 可以在这里做自己的改动
 * @doc https://umijs.org/docs/max/request#配置
 */
export const errorConfig: RequestConfig = {
  // 错误处理： umi@3 的错误处理方案。
  errorConfig: {
    // 错误抛出
    errorThrower: (res) => {
      const { success, data, errorCode, errorMessage, showType } =
        res as unknown as ResponseStructure;
      if (!success) {
        const error: any = new Error(errorMessage);
        error.name = 'BizError';
        error.info = { errorCode, errorMessage, showType, data };
        throw error; // 抛出自制的错误
      }
    },
    // 错误接收及处理
    errorHandler: async (error: any, opts: any) => {
      if (opts?.skipErrorHandler) throw error;

      // 我们的 errorThrower 抛出的错误。
      if (error.name === 'BizError') {
        const errorInfo: ResponseStructure | undefined = error.info;
        if (errorInfo) {
          const { errorMessage, errorCode } = errorInfo;
          switch (errorInfo.showType) {
            case ErrorShowType.SILENT:
              // do nothing
              break;
            case ErrorShowType.WARN_MESSAGE:
              message.warning(errorMessage);
              break;
            case ErrorShowType.ERROR_MESSAGE:
              message.error(errorMessage);
              break;
            case ErrorShowType.NOTIFICATION:
              notification.open({
                description: errorMessage,
                message: errorCode,
              });
              break;
            case ErrorShowType.REDIRECT:
              // TODO: redirect
              break;
            default:
              message.error(errorMessage);
          }
        }
      } else if (error.response) {
        // Axios 的错误
        // 请求成功发出且服务器也响应了状态码，但状态代码超出了 2xx 的范围
        const axiosError = error as AxiosError;

        if (axiosError?.response?.status === 403) {
          // 处理token过期，尝试刷新token
          const newToken = await refreshToken();
          if (newToken) {
            // Token刷新成功，重新发起请求
            // 注意：这里需要重新抛出一个特殊的错误，让调用方知道需要重试
            const retryError: any = new Error('Token refreshed, please retry');
            retryError.name = 'TokenRefreshed';
            retryError.newToken = newToken;
            throw retryError;
          }
          // Token刷新失败，不显示错误消息（已经在refreshToken中处理了登出）
        } else if (axiosError?.response?.status === 402) {
          // 处理授权问题，跳转到授权页面
          history.push('/authorization/shop-authorization');
          // 不显示错误消息，直接跳转
        } else if (error.response.status !== 200) {
          // 处理其他HTTP错误
          message.error(`${error.message}`);
        }
      } else if (error.request) {
        // 请求已经成功发起，但没有收到响应
        // \`error.request\` 在浏览器中是 XMLHttpRequest 的实例，
        // 而在node.js中是 http.ClientRequest 的实例
        message.error('None response! Please retry.');
      } else {
        // 发送请求时出了点问题
        message.error('Request error, please retry.');
      }
    },
  },

  // 请求拦截器
  requestInterceptors: [
    (url: string, options: RequestOptions) => {
      const accessToken = TokenManager.getAccessToken();
      const isGuestMode = localStorage.getItem('isGuestMode');

      const headers = {
        ...options.headers,
      };

      // 如果有access_token，添加到Authorization头
      if (accessToken) {
        headers['Authorization'] = `bearer ${accessToken}`;
      }

      if (isGuestMode === 'true') {
        headers['X-Demo-Mode'] = 'true';
      }

      return {
        url,
        options: { ...options, headers },
      };
    },
  ],

  // 响应拦截器
  responseInterceptors: [
    (response) => {
      // 拦截响应数据，进行个性化处理
      const { data } = response as unknown as ResponseStructure;

      // 处理API返回的错误格式 {code: xxx, message: "...", data: null}
      if (data && typeof data === 'object' && 'code' in data && 'message' in data) {
        if (data.code && data.code !== 200) {
          // 创建一个错误对象，确保能被useRequest的onError捕获
          const error: any = new Error(data.message);
          error.name = 'APIError';
          error.code = data.code;
          error.message = data.message;
          error.response = {
            status: data.code,
            data: data
          };
          throw error;
        }
      }

      if (data?.success === false) {
        message.error('请求失败！');
      }
      return response;
    },
  ],
};

// 导出 TokenManager 和相关方法
export { TokenManager, _performTokenRefreshAttempt };
